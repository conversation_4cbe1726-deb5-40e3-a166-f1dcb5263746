/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, model, Model, Schema, Types } from 'mongoose';
import { UniversalTrackerPlain } from './universalTracker';
import { OrderingDirection } from '../types/ordering';
import { ObjectId } from 'bson';
import { SurveyModelPlain } from './survey';

const MetricGroupShareSchema = new Schema({
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  created: { required: true, type: Date, default: Date.now },
  acceptedDate: Date,
},
  { _id: false }
)

export enum AccessType {
  Custom = 'custom',
  Assigned = 'assigned',
  Inherited = 'inherited',
}

export enum MetricActionType {
  Update = 'update',
  Ignore = 'ignore',
}

export enum MetricGroupType {
  Custom = 'custom',
  Tag = 'tag',
}

export enum CustomMetricOrderType {
  Name = 'name',
  TypeCode = 'typeCode',
  Custom = 'custom',
}

export interface CustomMetricsOrder {
  orderType: CustomMetricOrderType;
  // Used for non-custom order
  direction?: OrderingDirection;
}

const CustomMetricsOrderSchema = new Schema<CustomMetricsOrder>(
  {
    orderType: { type: Schema.Types.String, required: true, enum: Object.values(CustomMetricOrderType) },
    direction: {
      type: Schema.Types.String,
      enum: Object.values(OrderingDirection),
      required: false,
    },
  },
  { _id: false }
);

export enum MetricGroupSourceType {
  Survey = 'survey',
}

const metricGroupSourceSchema = new Schema<MetricGroupSource>(
  {
    type: {
      type: Schema.Types.String,
      enum: Object.values(MetricGroupSourceType),
      required: true,
    },
    surveyId: {
      type: Schema.Types.ObjectId,
      required: function (this: MetricGroupSource) {
        return this.type === MetricGroupSourceType.Survey;
      },
    },
    jobId: {
      type: Schema.Types.ObjectId,
    },
    topTopicsCount: {
      type: Schema.Types.Number,
      required: function (this: MetricGroupSource) {
        return this.type === MetricGroupSourceType.Survey;
      }
    },
    topicUtrs: {
      type: [{ _id: Schema.Types.ObjectId }],
      required: function (this: MetricGroupSource) {
        return this.type === MetricGroupSourceType.Survey;
      }
    },
  },
  { _id: false }
);

const MetricGroupSchema = new Schema<MetricGroupPlain>({
  initiativeId: { type: Schema.Types.ObjectId },
  type: { type: String, required: true, default: MetricGroupType.Custom, enum: Object.values(MetricGroupType) },
  universalTrackers: { type: [Schema.Types.ObjectId], required: false },
  groupName: String,
  description: { type: Schema.Types.String, required: false },
  groupData: {
    colour: String,
    link: String,
    icon: String,
    code: Schema.Types.String,
    preferredAltCodes: { type: [Schema.Types.String], default: undefined }
  },
  metricsOrder: CustomMetricsOrderSchema,
  share: { type: [MetricGroupShareSchema], default: [] },
  createdBy: Schema.Types.ObjectId,
  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now },
  source: { type: metricGroupSourceSchema },
  parentId: { type: Schema.Types.ObjectId, required: false },
}, { collection: 'kpi-groups', toJSON: { virtuals: true } });

MetricGroupSchema.virtual('initiative', {
  ref: 'Initiative', // The model to use
  localField: 'initiativeId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

MetricGroupSchema.virtual('share.initiative', {
  ref: 'Initiative', // The model to use
  localField: 'share.initiativeId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

MetricGroupSchema.virtual('universalTracker', {
  ref: 'UniversalTracker', // The model to use
  localField: 'universalTrackers', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: false,
});

MetricGroupSchema.virtual('survey', {
  ref: 'Survey',
  localField: 'source.surveyId',
  foreignField: '_id',
  justOne: true,
});

interface MetricGroupSurveySource {
  type: MetricGroupSourceType.Survey;
  surveyId: Types.ObjectId;
  // jobId is undefined, or the underlying score job was deleted
  jobId: Types.ObjectId | undefined;
  topTopicsCount: number;
  // distinguish between UTRS originating from Materiality Track and Company Tracker
  topicUtrs: { _id: ObjectId }[];
}

export type MetricGroupSource = MetricGroupSurveySource;

interface MetricGroupSharePlain<T = Types.ObjectId> {
  initiativeId: T;
  created?: Date;
  acceptedDate?: Date;
  initiative?: { name: string, profile: string },
}

export interface MetricGroupPlain<T = Types.ObjectId> {
  _id: T;
  initiativeId: T;
  initiative?: { name: string },
  type: string;
  description?: string;
  universalTrackers: T[];
  universalTracker?: UniversalTrackerPlain[],
  groupName: string;
  groupData: {
    colour: string,
    link: string,
    icon: string;
    preferredAltCodes?: string[];
    code?: string;
  };
  metricsOrder?: CustomMetricsOrder;
  share: MetricGroupSharePlain<T>[],
  updated: Date;
  createdBy: T;
  created?: Date;
  source?: MetricGroupSource;
  survey?: Pick<SurveyModelPlain, '_id' | 'type' | 'assessmentType' | 'effectiveDate' | 'completedDate' | 'initiativeId'>,
  parentId?: T;
}

export interface MetricGroupWithSubgroups<T = Types.ObjectId> extends MetricGroupPlain<T> {
  subgroups?: MetricGroupPlain<T>[];
}

export type MetricGroupModel = HydratedDocument<MetricGroupPlain>;

const MetricGroup: Model<MetricGroupPlain> = model('MetricGroup', MetricGroupSchema);
export default MetricGroup;
