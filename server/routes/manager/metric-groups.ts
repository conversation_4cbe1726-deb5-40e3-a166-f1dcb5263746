import { ObjectId } from 'bson';
import express from 'express';
import { isValidObjectId } from 'mongoose';
import { z } from 'zod';
import ContextError from '../../error/ContextError';
import { AuthRouter } from '../../http/AuthRouter';
import FileUpload from '../../http/FileUpload';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { checkIsStaff } from '../../middleware/userMiddlewares';
import Initiative, { InitiativeTypes } from '../../models/initiative';
import MetricGroup, { CustomMetricOrderType, MetricGroupType } from '../../models/metricGroup';
import UniversalTracker from '../../models/universalTracker';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { getAuditLogger } from '../../service/audit/AuditLogger';
import { InitiativeAudit } from '../../service/audit/events/Initiative';
import { LEVEL } from '../../service/event/Events';
import { uploadProfileFile } from '../../service/file/profile';
import { getMaterialityMetricGroupService } from '../../service/materiality-assessment/MaterialityMetricGroupService';
import { MetricGroupManager } from '../../service/metric/MetricGroupManager';
import { wwgLogger } from '../../service/wwgLogger';
import { mustValidate } from '../../util/validation';
import { metricGroupOrderUpdateDtoSchema } from '../validation-schemas/metric-groups';
import { MetricGroupRepository } from '../../repository/MetricGroupRepository';

const router = express.Router() as AuthRouter;
router.use(ContextMiddleware);

const auditLogger = getAuditLogger();

router
  .route('/')
  .get(async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const initiative = await Initiative.findById(initiativeId, { code: 1, type: 1 }).orFail().exec();
      if (initiative.type === InitiativeTypes.Group) {
        const metrics = await InitiativeRepository.getInitiativeKpiGroupsSingle(initiative);
        return res.FromModel(metrics);
      }
      const metrics = await InitiativeRepository.getInitiativeKpiGroups(initiativeId);
      for (const metric of metrics) {
        const subGroups = await MetricGroupRepository.getAllChildrenById(metric._id);
        console.log('subGroups', subGroups);
      }
      res.FromModel(metrics);
    } catch (e) {
      return next(e);
    }
  })
  .post(FileUpload.single('file'), async (req, res, next) => {
    const initiativeId = res.locals.initiativeId;

    const initiativeUtrIds = (await InitiativeRepository.getInitiativeKpis(initiativeId)).map((utr) =>
      utr._id.toString()
    );
    const filteredUtrIds = Array.isArray(req.body.universalTrackers)
      ? req.body.universalTrackers
          .filter((utrId: string) => initiativeUtrIds.includes(utrId))
          .map((utrId: string) => new ObjectId(utrId))
      : [];

    const metricGroup = new MetricGroup({
      initiativeId: initiativeId,
      type: req.params.type || MetricGroupType.Custom,
      universalTrackers: filteredUtrIds,
      groupName: req.body.groupName,
      description: req.body.description,
      groupData: {
        colour: req.body.groupData?.colour ?? '',
        link: req.body.groupData?.link ?? '',
        icon: req.body.groupData?.icon ?? '',
        preferredAltCodes: req.body.groupData?.preferredAltCodes,
      },
      metricsOrder: {
        orderType: CustomMetricOrderType.TypeCode,
      },
      updated: new Date(),
      createdBy: req.user._id,
    });
    try {
      if (req.file) {
        const { url } = await uploadProfileFile({
          id: metricGroup._id,
          type: 'metricGroup',
          file: req.file,
        });
        metricGroup.groupData.icon = url;
      }
      await metricGroup.save();

      auditLogger
        .fromRequest(req, {
          initiativeId: initiativeId,
          auditEvent: InitiativeAudit.metricGroupCreated,
          severity: LEVEL.INFO,
          targets: [auditLogger.metricGroupTarget(metricGroup)],
          message: auditLogger.buildMessage(InitiativeAudit.metricGroupCreated.description, req.body.groupName),
          debugData: { metricGroup: auditLogger.getMetricGroupDebugData(metricGroup) },
        })
        .catch(wwgLogger.error);

      res.FromModel(metricGroup);
    } catch (e) {
      next(e);
    }
  });

router
  .route('/:metricGroupId')
  .get((req, res, next) => {
    const initiativeId = res.locals.initiativeId; // Parsed by middleware
    MetricGroup.find({ _id: req.params.metricGroupId, initiativeId: initiativeId })
      .then((metricGroup) => res.FromModel(metricGroup))
      .catch(next);
  })
  .patch(FileUpload.single('file'), async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const metricGroup = await MetricGroup.findOne({ _id: req.params.metricGroupId, initiativeId: initiativeId })
        .orFail()
        .exec();
      metricGroup.groupName = req.body.groupName;
      metricGroup.description = req.body.description;
      metricGroup.groupData = {
        colour: req.body.groupData?.colour ?? '',
        link: req.body.groupData?.link ?? '',
        icon: req.body.groupData?.icon ?? '',
        preferredAltCodes: req.body.groupData?.preferredAltCodes,
      };
      metricGroup.updated = new Date();

      if (req.file) {
        const { url } = await uploadProfileFile({
          id: metricGroup._id,
          type: 'metricGroup',
          file: req.file,
        });
        metricGroup.groupData.icon = url;
      }

      await metricGroup.save();

      auditLogger
        .fromRequest(req, {
          initiativeId: initiativeId,
          auditEvent: InitiativeAudit.metricGroupUpdated,
          severity: LEVEL.INFO,
          targets: [auditLogger.metricGroupTarget(metricGroup)],
          message: auditLogger.buildMessage(InitiativeAudit.metricGroupUpdated.description, metricGroup.groupName),
        })
        .catch(wwgLogger.error);

      res.FromModel(metricGroup);
    } catch (e) {
      next(e);
    }
  })
  .delete(async (req, res, next) => {
    try {
      const metricGroup = await MetricGroup.findOne({
        _id: req.params.metricGroupId,
        initiativeId: res.locals.initiativeId, // Ensure we have access through initiativeId as well
      })
        .orFail()
        .exec();
      await metricGroup.deleteOne();

      auditLogger
        .fromRequest(req, {
          initiativeId: res.locals.initiativeId,
          auditEvent: InitiativeAudit.metricGroupDeleted,
          targets: [auditLogger.metricGroupTarget(metricGroup)],
          message: auditLogger.buildMessage(InitiativeAudit.metricGroupDeleted.description, metricGroup.groupName),
          debugData: { metricGroup: auditLogger.getMetricGroupDebugData(metricGroup) },
        })
        .catch(wwgLogger.error);

      res.Success(true);
    } catch (e) {
      next(e);
    }
  });

router.route('/:metricGroupId/order').patch(async (req, res, next) => {
  try {
    if (!isValidObjectId(res.locals.initiativeId) || !isValidObjectId(req.params.metricGroupId)) {
      return res.Exception(
        new ContextError('Invalid initiativeId or metricGroupId', {
          initiativeId: res.locals.initiativeId,
          metricGroupId: req.params.metricGroupId,
        })
      );
    }
    const data = mustValidate(req.body, metricGroupOrderUpdateDtoSchema);
    const initiativeId = new ObjectId(res.locals.initiativeId as string);
    const groupId = new ObjectId(req.params.metricGroupId);

    const metricGroup = await MetricGroupManager.updateMetricGroupUtrsOrder({ initiativeId, groupId, ...data });
    res.FromModel(metricGroup);
  } catch (error) {
    next(error);
  }
});

router.route('/:metricGroupId/regenerate').patch(async (req, res) => {
  const { topTopicsCount } = mustValidate(req.body, z.object({ topTopicsCount: z.number() }));

  const metricGroup = await getMaterialityMetricGroupService().regenerateMetricGroupUtrs({
    topTopicsCount,
    groupId: new ObjectId(req.params.metricGroupId),
  });

  res.FromModel(metricGroup);
});

router.route('/:metricGroupId/import').patch(checkIsStaff, async (req, res, next) => {
  try {
    const initiativeId = res.locals.initiativeId; // Parsed by middleware
    const metricGroup = await MetricGroup.findOne({
      _id: req.params.metricGroupId,
      initiativeId: initiativeId,
    })
      .orFail()
      .exec();

    const { replace, regenerate, data } = req.body;
    metricGroup.updated = new Date();

    if (!Array.isArray(data)) {
      return next(new Error('Data should be array'));
    }

    const codes = data.map((d: { QuestionCode: string }) => d.QuestionCode);

    // As this is staff only endpoint, we do not check if utrCodes are allowed,
    // but should verify specifically for private utr codes (ownerId is set)
    const utrs = await UniversalTracker.find(
      { code: { $in: codes } },
      {
        _id: 1,
        code: 1,
        ownerId: 1,
      }
    ).exec();

    const existingIds = metricGroup.universalTrackers.map(String);
    const newIds = utrs.map((u) => String(u._id));
    // Merge or use new ones
    const ids = replace ? newIds : [...existingIds, ...newIds];

    metricGroup.universalTrackers = Array.from(new Set(ids)).map((id) => new ObjectId(id));

    await metricGroup.save();

    wwgLogger.info('Imported metric group utrs', {
      importCodes: codes,
      importCount: codes.length,
      utrCodes: utrs.length,
      existingCount: existingIds.length,
      replace,
      regenerate,
    });

    auditLogger
      .fromRequest(req, {
        initiativeId: res.locals.initiativeId,
        auditEvent: InitiativeAudit.metricGroupDeleted,
        targets: [auditLogger.metricGroupTarget(metricGroup)],
        message: auditLogger.buildMessage(InitiativeAudit.metricGroupUpdated.description, metricGroup.groupName),
        debugData: { newIds, existingIds },
      })
      .catch(wwgLogger.error);

    res.FromModel({
      _id: metricGroup._id,
      universalTrackers: metricGroup.universalTrackers,
    });
  } catch (e) {
    next(e);
  }
});

router.route('/:metricGroupId/share/add').patch(async (req, res, next) => {
  try {
    const initiativeId = res.locals.initiativeId; // Parsed by middleware
    const metricGroup = await MetricGroup.findOne({ _id: req.params.metricGroupId, initiativeId: initiativeId })
      .orFail()
      .exec();
    metricGroup.updated = new Date();

    const shareId = req.body.initiativeId;
    metricGroup.share = metricGroup.share.filter((s) => String(s.initiativeId) !== shareId);
    metricGroup.share.push({
      initiativeId: shareId,
      acceptedDate: new Date(), // Auto-accept for now until we have an invitation/acceptance process
    });

    await metricGroup.save();
    res.FromModel(metricGroup);
  } catch (e) {
    next(e);
  }
});

router.route('/:metricGroupId/share').patch(async (req, res, next) => {
  try {
    const portfolioId = res.locals.initiativeId;
    const companies: string[] = req.body.initiativeIds ?? [];
    const metricGroupId = req.params.metricGroupId;
    const metricGroup = await MetricGroupManager.shareMultipleCompanies(portfolioId, metricGroupId, companies);
    res.FromModel(metricGroup);
  } catch (e) {
    next(e);
  }
});

router.route('/:metricGroupId/share/remove').patch(async (req, res, next) => {
  try {
    const initiativeId = res.locals.initiativeId; // Parsed by middleware
    const metricGroup = await MetricGroup.findOne({ _id: req.params.metricGroupId, initiativeId: initiativeId })
      .orFail()
      .exec();
    metricGroup.updated = new Date();

    const shareId = req.body.initiativeId;
    metricGroup.share = metricGroup.share.filter((s) => String(s.initiativeId) !== shareId);

    await metricGroup.save();
    res.FromModel(metricGroup);
  } catch (e) {
    next(e);
  }
});

router
  .route('/:metricGroupId/universal-tracker/:universalTrackerId')
  .post(async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const metricGroup = await MetricGroup.findOne({ _id: req.params.metricGroupId, initiativeId: initiativeId })
        .orFail()
        .exec();
      if (metricGroup.universalTrackers.some((id) => String(id) === req.params.universalTrackerId)) {
        return res.Invalid('Metric already in group');
      }

      metricGroup.universalTrackers.push(new ObjectId(req.params.universalTrackerId));
      metricGroup.updated = new Date();
      await metricGroup.save();

      auditLogger
        .fromRequest(req, {
          initiativeId: initiativeId,
          auditEvent: InitiativeAudit.metricGroupQuestionAdded,
          severity: LEVEL.INFO,
          targets: [
            auditLogger.metricGroupTarget(metricGroup),
            { type: 'UniversalTracker', id: req.params.universalTrackerId },
          ],
          message: auditLogger.buildMessage(
            InitiativeAudit.metricGroupQuestionAdded.description,
            metricGroup.groupName
          ),
        })
        .catch(wwgLogger.error);

      res.Success(true);
    } catch (e) {
      next(e);
    }
  })
  .delete(async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const utrId = req.params.universalTrackerId;
      const metricGroup = await MetricGroup.findOne({
        _id: req.params.metricGroupId,
        initiativeId: initiativeId,
      })
        .orFail()
        .exec();
      const position = metricGroup.universalTrackers.findIndex((u) => String(u) === utrId);

      if (position < 0) {
        return res.Invalid('Metric is not in group');
      }

      metricGroup.universalTrackers.splice(position, 1);
      metricGroup.updated = new Date();
      await metricGroup.save();

      auditLogger
        .fromRequest(req, {
          initiativeId: initiativeId,
          auditEvent: InitiativeAudit.metricGroupQuestionRemoved,
          targets: [auditLogger.metricGroupTarget(metricGroup), { type: 'UniversalTracker', id: utrId }],
          message: auditLogger.buildMessage(
            InitiativeAudit.metricGroupQuestionRemoved.description,
            metricGroup.groupName
          ),
        })
        .catch(wwgLogger.error);

      res.Success(true);
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
