/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express, { Response } from 'express';
import { ObjectId } from 'bson';
import { populateInitiativeByBody } from '../middleware/commonMiddlewares';
import Survey, {
  ScopeWheelPreferences,
  SurveyModel,
  SurveyModelPlain,
  SurveyType,
  SurveyWithInitiative
} from '../models/survey';
import { InitiativePlain } from '../models/initiative';
import { InitiativeOnboardingPlain, SurveyRoles } from '../models/onboarding';
import { addExpendedUsers, revertStakeholders } from '../service/stakeholder/StakeholderUtil';
import { getOnboardingManager, StakeholderTypes } from '../service/onboarding/OnboardingManager';
import { getSurveyManager } from '../service/survey/SurveyManager';
import { SurveyPermissions } from '../service/survey/SurveyPermissions';
import { DelegationPermissions } from '../service/delegation/DelegationPermissions';
import { SurveyRepository } from '../repository/SurveyRepository';
import ScorecardFactory from '../service/scorecard/ScorecardFactory';
import { createSurveyUsers } from '../service/survey/SurveyUsers';
import { SurveyDelegation } from '../service/survey/SurveyDelegation';
import { createSurveyRoleOnboarding } from '../service/survey/SurveyRoleOnboarding';
import { createSurveyDelegationScopeOnboarding } from '../service/survey/SurveyDelegationScopeOnboarding';
import { InitiativeRepository } from '../repository/InitiativeRepository';
import { getBlueprintManager } from '../service/survey/BlueprintManager';
import UserError from '../error/UserError';
import { getBluePrintContribution } from '../service/survey/BlueprintContribution';
import { Blueprints } from '../survey/blueprints';
import { getSurveyPublishManager } from '../service/survey/SurveyPublishManager';
import { getSurveyDeleteManager } from '../service/survey/SurveyDeleteManager';
import { getReportService } from '../service/survey/ReportData';
import { getSurveyDataTransfer } from '../service/survey/SurveyDataTransfer';
import { stringifyArrayCsvFile } from '../service/file/writer/CsvFileWriter';
import { DownLoadRequestDoc, DownloadScope } from '../service/survey/scope/downloadScope';
import {
  CsvColumnSetup,
  getCsvHeaders,
  getCsvName,
  surveyDownloadColumns,
} from '../service/assurance/csvContext';
import { Actions } from '../service/action/Actions';
import { setCsvFileHeaders, setXlsxFileHeaders } from '../http/FileDownload';
import { clearCache } from '../service/cache';
import { initiativeMinFields } from '../repository/projections';
import { getSurveyClone, SurveyClone } from '../service/survey/SurveyClone';
import { SurveyImporter } from '../service/survey/SurveyImporter';
import { AuthenticatedRequest, AuthRouter } from '../http/AuthRouter';
import { StakeholderGroup } from '../models/public/universalTrackerValueType';
import { SurveyErrorMessages } from '../error/ErrorMessages';
import BadRequestError from '../error/BadRequestError';
import { getDataImporter } from '../service/survey/DataImporter';
import { getFileImporter } from '../service/survey/FileImporter';
import { FileParserType } from '../service/survey/transfer/parserTypes';
import FileUpload from '../http/FileUpload';
import { DataTransform } from '../service/survey/transfer/DataTransform';
import { getFileExporter } from '../service/survey/FileExporter';
import { write } from '@sheet/core';
import { getUserEventService } from '../service/event/UserEventService';
import { LEVEL, SurveyEvents } from '../service/event/Events';
import { wwgLogger } from '../service/wwgLogger';
import { ContextMiddleware } from '../middleware/audit/contextMiddleware';
import { SurveyAudit } from '../service/audit/events/survey';
import { getAuditLogger } from '../service/audit/AuditLogger';
import { AuditEvent } from '../service/audit/AuditModels';
import { UserPlain } from '../models/user';
import { VisibilityStatus } from '../service/survey/scope/visibilityStatus';
import { canManageSurvey, canViewSurvey } from '../middleware/surveyMiddlewares';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import { MetricActionType, MetricGroupType } from '../models/metricGroup';
import { simpleDownloadHandler } from './handlers/download-handlers';
import moment from 'moment';
import { getUtrvDelegationUsers } from '../service/delegation/UtrvDelegationUsers';
import { getPPTXReportingService } from '../service/pptx-report/PPTXReportService';
import { toBoolean } from '../http/query';
import { getGenerateReportService } from '../service/pptx-report/GenerateReportService';
import { PPTXTemplateName } from '../service/pptx-report/types';
import { UtrSurveysService } from '../service/survey/UtrSurveysService';
import { getUtrvCommentRepository } from '../repository/UtrvCommentRepository';
import { getRootInitiativeService } from '../service/organization/RootInitiativeService';
import { FeatureCode } from '@g17eco/core';
import { SURVEY } from '../util/terminology';
import { TaskType } from '../models/backgroundJob';
import { getPPTXReportManager } from '../service/pptx-report/PPTXReportManager';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { mustValidate } from '../util/validation';
import { pptxReportQuerySchema } from '../routes/validation-schemas/pptx-report';
import { z } from 'zod';
import { isCTStarter } from '../util/initiative';
import { getSurveyAggregator } from '../service/survey/SurveyAggregator';
import { AggregatedCloneParams, CloneParams } from '../service/survey/model/ProcessData';
import { getSurveyWorkgroupService } from '../service/workgroup/SurveyWorkgroupService';
import { DataScopeAccess } from '../models/dataShare';
import { downloadDelegatedSurveysSchema, surveyPermissionSchema } from './validation-schemas/survey';
import { getObjectIdsSchema, refineIdSchema } from './validation-schemas/common';
import { canAccessInitiative } from '../middleware/initiativeMiddlewares';
import { getDelegatedSurveysReportDownload } from '../service/survey/download/DelegatedSurveysReportDownload';
import { SurveyScope } from '../service/survey/SurveyScope';
import { getExcel } from '../service/file/Excel';
import { EMISSION_METRICS_MAPPING } from '../service/survey/constants';
import ContextError from '../error/ContextError';
import { SurveyUserRoles } from '../types/roles';

const deleteManager = getSurveyDeleteManager();
const publishManager = getSurveyPublishManager();
const onboardingManager = getOnboardingManager();
const surveyManager = getSurveyManager();
const delegationPermissions = new DelegationPermissions();
const surveyUsers = createSurveyUsers();
const surveyRoleOnboarding = createSurveyRoleOnboarding();
const surveyDelegationScopeOnboarding = createSurveyDelegationScopeOnboarding();
const blueprintManager = getBlueprintManager();
const bc = getBluePrintContribution();
const surveyDataTransfer = getSurveyDataTransfer();
const surveyClone = getSurveyClone();
const userEvents = getUserEventService();
const auditLogger = getAuditLogger();
const utrvCommentRepository = getUtrvCommentRepository();
const surveyAggregator = getSurveyAggregator();
const surveyWorkgroupService = getSurveyWorkgroupService();
const delegatedSurveysReportDownload = getDelegatedSurveysReportDownload();

const addAudit = (survey: SurveyModelPlain, auditEvent: AuditEvent, severity?: LEVEL, message?: string, debugData?: Record<string, unknown>) => {
  auditLogger.fromContext({
    initiativeId: survey.initiativeId,
    auditEvent: auditEvent,
    severity,
    targets: [auditLogger.surveyTarget(survey)],
    message: message,
    debugData,
  }).catch(wwgLogger.error)
};

const router = express.Router({ mergeParams: true }) as AuthRouter;

const paramIdCheck = (params: string[]) => (req: any, res: any, next: any) => {
  try {
    for (const param of params) {
      if (req.params[param]) {
        req.params[param] = new ObjectId(req.params[param]);
      }
    }
  } catch (e) {
    return res.Invalid('Survey id is not valid', params, req.params);
  }

  next();
};

type CheckIsSurveyResponse = Response<any, Record<string, SurveyModel>>;

router.route('/')
  .post(populateInitiativeByBody, async (req, res) => {
    try {
      const uniqCode = Math.random().toString(36).substring(7);
      const initiative = req.initiative;
      if (!initiative) {
        return res.Exception('Invalid reporting level');
      }

      const isManager = await InitiativePermissions.canManageInitiative(req.user, initiative._id);
      if (!isManager) {
        return res.NotPermitted();
      }

      const props = {
        code: `${initiative.code}/${req.body.sourceName}/${req.body.year}/${uniqCode}`,
        name: `${initiative.name} - CDP (${req.body.year})`,
        sourceName: req.body.sourceName,
        effectiveDate: moment(req.body.year + '-12-31').toISOString(),
        utrvType: 'actual',
        initiativeId: req.body.initiativeId,
        stakeholders: {
          stakeholder: [req.user._id],
        },
        evidenceRequired: req.body.evidenceRequired,
        noteRequired: req.body.noteRequired,
        verificationRequired: req.body.verificationRequired,
      };
      const survey = new Survey(props);
      // const result = await SurveyImporter.process(req.user, survey);
      res.FromModel(survey);
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/import/file/mapping')
  .get((req, res) => {
    res.FromModel({
      mapping: DataTransform.getDefaultMapping(),
      tabName: ['questions', 'Questions'],
    })
  })

router.route('/:surveyId')
  .get(paramIdCheck(['surveyId']), async (req, res) => {
    try {
      const survey = await SurveyRepository.getSurveyData(req.params.surveyId, req.user, req.header('origin'));
      if (!survey) {
        return res.FromModel();
      }

      const workgroups = await surveyWorkgroupService.getWorkgroups({ survey, userIds: [req.user._id] });
      return res.FromModel({ ...survey, workgroups });
    } catch (e) {
      res.Exception(e);
    }
  })
  .delete(ContextMiddleware, canManageSurvey,
    async (req, res: CheckIsSurveyResponse) => {
      try {
        const { survey } = res.locals;
        await deleteManager.disableSurvey(survey, req.user);
        addAudit(survey, SurveyAudit.disable, LEVEL.WARNING);
        clearCache();
        return res.Success(`Disabled survey ${survey._id} successfully`);
      } catch (e) {
        res.Exception(e);
      }
    })

router.route('/:surveyId/clone').post(canManageSurvey, async (req, res: CheckIsSurveyResponse, next) => {
  try {
    const { survey } = res.locals;
    if (!(await InitiativePermissions.canManageInitiative(req.user, survey.initiativeId))) {
      return res.NotPermitted();
    }

    await survey.populate('initiative');

    const isCombinedReport = survey.type === SurveyType.Aggregation;
    const { year, month, ...rest } = req.body;
    const m = month - 1; // JS starts from 0
    const effectiveDate = moment().year(year).month(m).endOf('month').startOf('day').toDate();
    const options = SurveyClone.createDuplicateOption({ ...rest, effectiveDate, user: req.user });

    const params = isCombinedReport
      ? await surveyClone.createAggregatedCloneParams(survey, options)
      : await surveyClone.createCloneParams(survey, options);

    const data = isCombinedReport
      ? await surveyAggregator.cloneAggregatedSurvey(params as AggregatedCloneParams)
      : await SurveyImporter.clone(params as CloneParams);

    res.FromModel(data);
  } catch (e) {
    next(e);
  }
});


router.route('/:surveyId/complete')
  .patch(ContextMiddleware, canManageSurvey,
    async (req, res: CheckIsSurveyResponse) => {
      try {
        const { survey } = res.locals;
        await publishManager.completeSurvey(survey, req.user);

        clearCache();
        return res.Success(`Marking survey ${survey._id} as completed successfully`);

      } catch (e) {
        res.Exception(e)
      }
    })
  .delete(ContextMiddleware, canManageSurvey,
    async (req, res: CheckIsSurveyResponse) => {
      try {
        const { survey } = res.locals;
        await publishManager.uncompleteSurvey(survey, req.user);

        return res.Success(`Marking survey ${survey._id} as not completed successfully`);

      } catch (e) {
        res.Exception(e)
      }
    })

router.route('/:surveyId/questions').get(canManageSurvey, async (req, res: CheckIsSurveyResponse) => {
  const { survey } = res.locals;
  const utrs = await bc.getQuestionsWithCustomMetrics(survey.sourceName as Blueprints, survey.initiativeId);
  return res.FromModel(utrs);
});

router.route('/:surveyId/metric-groups')
  .get(canViewSurvey,
    async (req, res: CheckIsSurveyResponse, next) => {
      try {
        const { survey } = res.locals;
        InitiativeRepository.getInitiativeKpiGroups(survey.initiativeId, MetricGroupType.Custom)
          .then((metrics) => res.FromModel(metrics)).catch(next);
      } catch (e) {
        return next(e)
      }
    })

// TODO: middleware check fails for Assurance Tracker, need to create a new endpoint
router.route('/:surveyId/comments/count').get(canViewSurvey, async (req, res, next) => {
  try {
    const utrvIds = (res.locals.survey.visibleUtrvs ?? []) as ObjectId[];
    const result = await utrvCommentRepository.countCommentsByUtrvIds(utrvIds);
    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router.route('/:surveyId/config')
  .get(canViewSurvey, paramIdCheck(['surveyId']), async (req, res: CheckIsSurveyResponse, next) => {
    try {
      const config = await surveyManager.getSurveyConfiguration(res.locals.survey);
      return res.FromModel(config);
    } catch (e) {
      next(e);
    }
  })
  .patch(ContextMiddleware, paramIdCheck(['surveyId']), canManageSurvey,
    async (req, res: CheckIsSurveyResponse, next) => {
      try {
        const { survey } = res.locals;
        const original = survey.toObject();
        await survey.populate('initiative');
        const update = await surveyManager.updateSettings(survey, req.user, req.body);
        if (update) {
          addAudit(
            survey,
            SurveyAudit.configurationUpdate,
            LEVEL.INFO,
            auditLogger.buildMessage(
              SurveyAudit.configurationUpdate.description,
              auditLogger.getPreferredSurveyName(survey, survey.initiative)
            ),
            { settings: auditLogger.debugSettingsChange({ before: original, after: update }) },
          );
        }

        const config = await surveyManager.getSurveyConfiguration(survey);
        return res.FromModel(config);
      } catch (e) {
        next(e);
      }
    });

const scopeHandler = async (req: AuthenticatedRequest, res: CheckIsSurveyResponse) => {
  try {
    const action = req.method.toLowerCase() === 'delete' ? Actions.Remove : Actions.Add
    const { survey } = res.locals;
    await survey.populate('initiative');
    const updatedSurvey = await surveyManager.updateSurveyScope({
      delegator: req.user,
      surveyId: req.params.surveyId,
      action: action,
      scopeGroups: req.body.scopeGroups,
      domain: req.header('origin'),
    });

    const event = action === Actions.Remove ? SurveyAudit.scopeRemoved : SurveyAudit.scopeAdded
    auditLogger.fromRequest(req, {
      initiativeId: survey.initiativeId,
      auditEvent: event,
      targets: [auditLogger.surveyTarget(survey)],
      message: auditLogger.buildMessage(event.description, auditLogger.getPreferredSurveyName(survey, survey.initiative)),
      debugData: { action, scopeGroups: req.body.scopeGroups },
    }).catch(wwgLogger.error);
    return res.FromModel(updatedSurvey)

  } catch (e) {
    res.Exception(e);
  }
};

router.route('/:surveyId/scope')
  .patch(paramIdCheck(['surveyId']), canManageSurvey, scopeHandler)
  .delete(paramIdCheck(['surveyId']), canManageSurvey, scopeHandler);

router.route('/:surveyId/blueprint')
  .patch(paramIdCheck(['surveyId']), canManageSurvey,
    async (req, res: CheckIsSurveyResponse) => {
      const { survey } = res.locals;
      await blueprintManager.addUtrGroup(survey, req.body)
        .then((r) => res.FromModel(r))
        .catch(e => res.Exception(e));
    });

Object.values(MetricActionType).forEach(action => {

router.route(`/:surveyId/blueprint/${action}`)
  .patch(canManageSurvey, async (req, res, next) => {
    try {
      const { surveyId } = req.params;
      switch (action) {
        case MetricActionType.Update:
          await surveyManager.regenerateBlueprintById(surveyId, req.user);
          return res.Success(true);
        case MetricActionType.Ignore:
          await surveyManager.ignoreMetricGroupChanges(surveyId);
          return res.Success(true);
        default:
          throw new Error(`Action "${action}" is not supported`);
      }
    } catch (e) {
      next(e);
    }
  });
});


router.route('/:surveyId/users/:role')
  .get(paramIdCheck(['surveyId']), canViewSurvey,
    async (req, res: CheckIsSurveyResponse) => {
      const { survey } = res.locals;
      await surveyUsers.getSurveyUsersByRole(
        survey,
        req.params.role as keyof SurveyRoles,
        req.user
      )
        .then(r => res.FromModel(r))
        .catch(e => res.Exception(e));
    });

const delegationRoleHandler = async (req: AuthenticatedRequest, res: Response) => {
  const processRequest = SurveyDelegation.fromRoleRequest(req);
  SurveyDelegation.processRoleUpdate(processRequest)
    .then((r) => res.FromModel(r))
    .catch((e) => res.Exception(e));
};

const delegationScopeHandler = async (action: Actions, req: AuthenticatedRequest, res: Response) => {
  const { userId, role, id } = req.params;
  if (req.body.scopeGroups) {
    // Scope level delegation
    const processRequest = SurveyDelegation.fromRequest(req);
    SurveyDelegation.processDelegationRequest(processRequest)
      .then((r) => res.FromModel(r))
      .catch((e) => res.Exception(e));
  } else {
    // Survey level delegation
    surveyManager
      .delegationAction(id, userId, role as StakeholderTypes, action, req.user)
      .then((resp: Boolean) => res.Success(resp))
      .catch((e: Error) => res.Exception(e));
  }
};

const getDelegationHandlerByRole = async (action: Actions, req: AuthenticatedRequest, res: Response) => {
  const { role } = req.params;
  switch (role) {
    case SurveyUserRoles.Admin:
    case SurveyUserRoles.Viewer: {
      return delegationRoleHandler(req, res);
    }
    case SurveyUserRoles.Stakeholder:
    case SurveyUserRoles.Verifier: {
      return delegationScopeHandler(action, req, res);
    }
    default: {
      wwgLogger.error(new ContextError(`Unsupported delegation role ${role}`, { surveyId: req.params.id, delegatorId: req.user.id, userId: req.params.userId }));
      throw new BadRequestError(`Unsupported delegation role ${role}`);
    }
  }
};
router
  .route('/:id/users/:role/:userId')
  .patch(paramIdCheck(['id']), ContextMiddleware, (req, res) => getDelegationHandlerByRole(Actions.Add, req, res))
  .delete(paramIdCheck(['id']), ContextMiddleware, (req, res) => getDelegationHandlerByRole(Actions.Remove, req, res));

const onboardingRoleHandler = async (req: AuthenticatedRequest, res: Response) => {
  const processRequest = surveyRoleOnboarding.fromRoleOnboardingRequest(req);
  surveyRoleOnboarding.processRoleOnboarding(processRequest)
    .then((r) => res.FromModel(r))
    .catch((e) => res.Exception(e))
};

const delegationScopeOnboardingHandler = async (req: AuthenticatedRequest, res: Response) => {
  const processRequest = surveyDelegationScopeOnboarding.fromRoleOnboardingRequest(req);
  surveyDelegationScopeOnboarding
    .processRoleOnboarding(processRequest)
    .then((r) => res.FromModel(r))
    .catch((e) => res.Exception(e));
};

const onboardingStakeholderDelegationHandler = async (action: Actions, req: AuthenticatedRequest, res: Response) => {
  if (req.body.scopeGroups) {
    // scopeGroups implies it should have been
    return delegationScopeOnboardingHandler(req, res);
  }
  const { email, role, id } = req.params;
  if (action === Actions.Add) {
    onboardingManager
      .onboardSurvey(email, role as StakeholderTypes, id, req.user, req.body)
      .then((resp) => res.FromModel(resp))
      .catch((e: Error) => res.Exception(e));
  } else {
    onboardingManager
      .removeOnboardSurvey(email, role as StakeholderTypes, id, req.user)
      .then((resp: Boolean) => res.Success(resp))
      .catch((e: Error) => res.Exception(e));
  }
};

const getOnboardingDelegationHandlerByRole = async (action: Actions, req: AuthenticatedRequest, res: Response) => {
  const { role } = req.params;
  switch (role) {
    case SurveyUserRoles.Admin:
    case SurveyUserRoles.Viewer: {
      return onboardingRoleHandler(req, res);
    }
    case SurveyUserRoles.Stakeholder:
    case SurveyUserRoles.Verifier: {
      return onboardingStakeholderDelegationHandler(action, req, res);
    }
    default: {
      wwgLogger.error(
        new ContextError(`Unsupported onboarding delegation role ${role}`, {
          userId: req.user.id,
          surveyId: req.params.id,
          email: req.params.email,
        })
      );
      throw new BadRequestError(`Unsupported onboarding delegation role ${role}`);
    }
  }
};

/**
 * @deprecated Don't allow delegate at survey level via onboarding email anymore. Must be active user.
 * FE still has deprecated code using this. Need to remove from both sides.
 */
router
  .route('/:id/users/:role/:email/onboard')
  .patch(ContextMiddleware, (req, res) => getOnboardingDelegationHandlerByRole(Actions.Add, req, res))
  .post(ContextMiddleware, (req, res) => getOnboardingDelegationHandlerByRole(Actions.Add, req, res))
  .delete(ContextMiddleware, (req, res) => getOnboardingDelegationHandlerByRole(Actions.Remove, req, res));

router.route('/:surveyId/users')
  .get(paramIdCheck(['surveyId']), canViewSurvey,
    async (req, res: CheckIsSurveyResponse) => {
      try {
        const { survey } = res.locals;
        const users = await surveyUsers.getSurveyUsers(survey, req.user);
        const onboardings = await surveyDelegationScopeOnboarding.getSurveyOnboardings(survey, req.user);
        return res.FromModel({ users: [...users, ...onboardings] });
      } catch (e) {
        res.Exception(e);
      }
    });

router.route(`/download/delegated`).post(async (req, res) => {
    const { initiativeId, userIds, surveyIds, downloadScope, type } = mustValidate(
      req.body,
      downloadDelegatedSurveysSchema
    );

    const downloadMultiScope = {
      ...downloadScope,
      /** @todo: [GU-6024] Since we have no scope, we are filtering out the data later using the access to initiative and surveys. */
      access: DataScopeAccess.Full,
      scope: SurveyScope.createEmpty(),
    };

    const downloadData = await delegatedSurveysReportDownload.getDownloadData({
      initiativeId,
      userIds,
      surveyIds,
      downloadScope: downloadMultiScope,
    });

    const exportType = type === FileParserType.Xlsx ? FileParserType.Xlsx : FileParserType.Csv;
    const data = await delegatedSurveysReportDownload.getFileContent({
      exportType,
      downloadData,
    });

    const filename = 'Delegated Surveys Data Report';
    const fullFilename = `${filename}.${exportType}`;
    if (exportType === FileParserType.Csv) {
      setCsvFileHeaders(res, fullFilename);
    } else {
      setXlsxFileHeaders(res, fullFilename);
    }

    wwgLogger.info(`${exportType} file generated successfully`, { initiativeId, userIds, surveyIds });
    return res.send(data);
  });

['csv', 'xlsx'].forEach(type => {

router.route(`/:surveyId/download/${type}/simple`)
  .post(paramIdCheck(['surveyId']), async (req, res) => {
    try {
      const survey = await Survey.findById(req.params.surveyId)
        .populate('initiative')
        .exec() as SurveyWithInitiative;

      if (!survey || !survey.initiative) {
        return res.FromModel(survey);
      }

      const downloadScope = await DownloadScope.fromMultiScopeRequest(req, survey, VisibilityStatus.ExcludeData);
      return await simpleDownloadHandler({
        downloadScope,
        res,
        survey,
        type,
      });
    } catch (e) {
      res.Exception(e);
    }
  });
});

router.route(['', 'standards', 'frameworks'].map(scopeType => `/:surveyId/download${scopeType ? `/${scopeType}` : ''}`))
  .post(async (req, res) => {
    try {
      const survey = await SurveyRepository.mustFindById(req.params.surveyId);
      if (!await SurveyPermissions.canAccess(survey, req.user)) {
        return res.NotPermitted();
      }

      const ids = req.body.ids;
      if (!Array.isArray(ids)) {
        return res.Exception(new BadRequestError(SurveyErrorMessages.DownloadNoQuestions))
      }
      const visibleIds = new Set(survey.visibleUtrvs.map(String));
      const validIds = ids.filter(id => visibleIds.has(id)).map(id => new ObjectId(id))
      if (validIds.length === 0) {
        return res.Exception(new BadRequestError(SurveyErrorMessages.DownloadNoQuestions))
      }

      const result = await surveyDataTransfer.createCsvForUtrvs({
        utrvIds: validIds,
        user: req.user,
        date: survey.effectiveDate,
        columnSetup: surveyDownloadColumns,
      });
      setCsvFileHeaders(res, result.filename);
      return res.send(stringifyArrayCsvFile(result));
    } catch (e) {
      res.Exception(e);
    }
  })

router
  .route('/:surveyId/export/xlsx')
  .get(paramIdCheck(['surveyId']), canViewSurvey, async (req, res: CheckIsSurveyResponse) => {
    const { survey } = res.locals;
    const exportType = FileParserType.Xlsx;
    const exporter = getFileExporter();
    const result = await exporter.exportXlsx({
      user: req.user,
      survey,
      type: exportType,
      preferredTypes: survey.scope?.standards ?? [],
    });

    await survey.populate('initiative');
    const filename = getCsvName({ initiative: survey.initiative, survey, _id: survey._id });
    setXlsxFileHeaders(res, `${filename}.${exportType}`);
    return res.send(write(result, { type: 'buffer', bookType: exportType, cellStyles: true }));
  });

/** @deprecated not used? */
router.route('/:surveyId/export/import-csv')
  .get(paramIdCheck(['surveyId']), canViewSurvey,
    async (req, res: CheckIsSurveyResponse) => {
      try {
        const { survey } = res.locals;
        const fields = ['QuestionCode', 'Question', 'Value', 'Comment'];

        const result = await surveyDataTransfer.createCsvForUtrvs({
          utrvIds: survey.visibleUtrvs,
          user: req.user,
          date: survey.effectiveDate,
          includeComplexType: false,
          columnSetup: fields.map(f => getCsvHeaders().find((h) => h.name === f)) as CsvColumnSetup[]
        });

        setCsvFileHeaders(res, result.filename);
        return res.send(stringifyArrayCsvFile(result));
      } catch (e) {
        res.Exception(e);
      }
    })
  .patch(paramIdCheck(['surveyId']), canManageSurvey, ContextMiddleware,
    async (req, res: CheckIsSurveyResponse) => {
      try {
        const { survey } = res.locals;
        const dataImporter = getDataImporter();
        const updates = await dataImporter.process({
          user: req.user,
          survey,
          data: req.body.data,
          mapping: req.body.mapping,
        });

        const results = await dataImporter.updateActionRequests(updates, req.user)
        clearCache();
        res.Success(`Successfully updated ${results.length} questions`);
      } catch (e) {
        res.Exception(e);
      }
    })

router
  .route('/:surveyId/import/file')
  .post(
    paramIdCheck(['surveyId']),
    canManageSurvey,
    FileUpload.single('file'),
    ContextMiddleware,
    async (req, res: CheckIsSurveyResponse) => {
      const file = req.file;
      if (!file || !file.path) {
        return res.Exception(`Missing required import file`);
      }
      const filepath = file.path;
      const { survey } = res.locals;
      const fileImporter = getFileImporter();
      const fileType = file.originalname.split('.').pop() || 'xlsx';
      const isFileExcel = fileType === FileParserType.Xlsx;
      const workbook = isFileExcel ? await getExcel().readFile(filepath) : undefined;
      const isSGXImport = workbook?.SheetNames.includes('GUIDANCE');
      
      const updates = await fileImporter.processFile(survey, req.user, {
        type: fileType,
        mapping: req.body.mapping,
        filepath: filepath,
        tab: isFileExcel ? { name: req.body.tabName || 'question' } : undefined,
        metricsMapping: isSGXImport ? EMISSION_METRICS_MAPPING : undefined,
      });

      const results = await fileImporter.saveUpdates(updates, req.user);
      clearCache();
      res.Success(`Successfully updated ${results.length} questions`);
    }
  );


router.route('/user/actions')
  .get(async (req, res) => {
    try {
      const userId = req.user._id;
      const surveyActions = await SurveyRepository.getSurveyStatsByUserId(userId);
      const scorecardFactory = new ScorecardFactory();
      const actionWithScorecard = await Promise.all(surveyActions.map(async (action) => ({
        ...action,
        scorecard: await scorecardFactory.getGoalScoresBySurveyId(action._id),
      })));

      return res.FromModel(actionWithScorecard);
    } catch (e) {
      res.Exception(e);
    }
  });

/** @deprecated: new behavior is limited to current initiative */
router.route('/user/actions/org/:orgId')
  .get(async (req, res) => {
    const user = req.user as UserPlain;
    const children = await InitiativeRepository.getMainTreeChildren(req.params.orgId)
    const ids = children.map(i => i._id);

    const surveyActions = await SurveyRepository.getSurveyStatsByUserId(user._id, ids);

    const scorecardFactory = new ScorecardFactory();
    const actionWithScorecard = await Promise.all(surveyActions.map(async (action) => ({
      ...action,
      scorecard: await scorecardFactory.getGoalScoresBySurveyId(action._id),
    })));

    return res.FromModel(actionWithScorecard);
  });

router.route('/user/actions/initiative/:initiativeId')
  .get(canAccessInitiative, async (req, res) => {
    const user = req.user as UserPlain;
    const initiativeId = new ObjectId(req.params.initiativeId);
    const surveyActions = await SurveyRepository.getSurveyStatsByUserId(user._id, [initiativeId]);

    const scorecardFactory = new ScorecardFactory();
    const actionWithScorecard = await Promise.all(
      surveyActions.map(async (action) => ({
        ...action,
        scorecard: await scorecardFactory.getGoalScoresBySurveyId(action._id),
        workgroups: await getSurveyWorkgroupService().getWorkgroups({ survey: action, userIds: [user._id], utrvs: [] }), // Used for check user survey permissions in FE. So don't need to fetch utrvs.
      }))
    );

    return res.FromModel(actionWithScorecard);
  });

// Min survey data with initiative: used by getSurveyListItem frontend
router.route('/:surveyId/initiative')
  .get(async (req, res, next) => {
    try {
      const { surveyId } = req.params;
      const survey = await Survey.findById(surveyId, {
        name: 1,
        initiativeId: 1,
        effectiveDate: 1,
        completedDate: 1,
        visibleStakeholders: 1,
        period: 1,
        sourceName: 1,
        unitConfig: 1,
      })
        .populate({ path: 'initiative', select: initiativeMinFields })
        .exec();

      if (!survey) {
        return res.Exception(new UserError(`${SURVEY.CAPITALIZED_SINGULAR} with id '${surveyId}' was not found `));
      }

      const hasAccess = await SurveyPermissions.canAccess(survey, req.user);
      if (!hasAccess) {
        return res.NotPermitted();
      }
      res.FromModel(survey)
    } catch (e) {
      next(e)
    }
  })

router.route('/:surveyId/stakeholders')
  .get((req, res) => {
    SurveyRepository.getStakeholders(req.params.surveyId)
      .then(([survey]: any[]) => {
        if (!survey) {
          return res.FromModel(survey);
        }

        const surveyId = survey._id.toString();
        return onboardingManager.findOnboardingSurveyUsers(survey._id, survey.initiativeId)
          .then(async (data: InitiativeOnboardingPlain[]) => {
            data.forEach(({ user, surveyStakeholders }) => {
              addExpendedUsers(survey, surveyStakeholders as StakeholderGroup, surveyId, user);
            });

            const permissions = await delegationPermissions.getPermissions({
              user: req.user,
              survey: { ...survey, stakeholders: revertStakeholders(survey) },
            });

            res.FromModel({ ...survey, permissions });
          });
      })
      .catch((e: Error) => res.Exception(e));
  });


router.route('/:surveyId/universal-tracker-values/flags')
  .post(paramIdCheck(['surveyId']), canManageSurvey,
    async (req: AuthenticatedRequest, res: CheckIsSurveyResponse, next) => {
      try {
        const { survey } = res.locals;
        const { utrvIds, properties } = req.body;
        if (!Array.isArray(utrvIds) || typeof properties !== 'object') {
          return next(new Error(`Missing utrvs or properties in request body`))
        }

        const response = await surveyManager.updateUtrvSettings(survey, req.user, { utrvIds, properties });
        wwgLogger.info(`Updated survey bulk flags`, {
          properties,
          initiativeId: survey.initiativeId,
          surveyId: survey._id,
          modified: response ? response.modifiedCount : 0,
        })

        if (response) {
          userEvents.addSurveyEvent(req.user, SurveyEvents.BulkAction, {
            ...req.body,
            modified: response.modifiedCount,
            initiativeId: survey.initiativeId,
            surveyId: survey._id,
          })
        }

        res.FromModel({
          properties,
          initiativeId: survey.initiativeId,
          surveyId: survey._id,
          modified: response ? response.modifiedCount : 0,
        });
      } catch (e) {
        res.Exception(e)
      }
    })

const massDelegationHandler = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const data = await SurveyDelegation.fromMassAction(req);
    const response = await SurveyDelegation.processMassDelegation(data);
    res.FromModel(response);
  } catch (e) {
    res.Exception(e)
  }
};



router
  .route('/:surveyId/workgroups')
  .get(paramIdCheck(['surveyId']), canViewSurvey, async (req, res, next) => {
    try {
      const workgroups = await surveyWorkgroupService.getWorkgroups({ survey: res.locals.survey as SurveyModel });
      res.FromModel(workgroups);
    } catch (e) {
      next(e);
    }
  })
  .post(paramIdCheck(['surveyId']), canManageSurvey, async (req, res, next) => {
    try {
      const { workgroupIds, permission } = mustValidate(
        req.body,
        z.object({
          workgroupIds: getObjectIdsSchema({ min: 1, isOptional: false }),
          permission: surveyPermissionSchema,
        })
      );
      const result = await surveyWorkgroupService.delegateWorkgroups({
        survey: res.locals.survey as SurveyModel,
        workgroupIds,
        permission,
      });
      res.FromModel(result);
    } catch (e) {
      next(e);
    }
  })
  .put(paramIdCheck(['surveyId']), canManageSurvey, async (req, res) => {
    const { workgroupId, permission } = mustValidate(
      req.body,
      z.object({
        workgroupId: refineIdSchema('workgroupId'),
        permission: surveyPermissionSchema,
      })
    );
    const result = await surveyWorkgroupService.updateWorkgroup({
      survey: res.locals.survey as SurveyModel,
      workgroupId,
      permission,
    });
    res.FromModel(result);
  })
  .delete(paramIdCheck(['surveyId']), canManageSurvey, async (req, res) => {
    const { workgroupIds } = mustValidate(
      req.body,
      z.object({
        workgroupIds: getObjectIdsSchema({ min: 1, isOptional: false }),
      })
    );
    const result = await surveyWorkgroupService.removeWorkgroups({
      survey: res.locals.survey as SurveyModel,
      workgroupIds,
    });
    res.FromModel(result);
  });

router.route('/:id/universal-tracker-values/delegation')
  .post(paramIdCheck(['id']), ContextMiddleware, massDelegationHandler)
  .delete(paramIdCheck(['id']), ContextMiddleware, massDelegationHandler);

router.route('/:surveyId/universal-tracker-values/users')
  .post(paramIdCheck(['surveyId']), canViewSurvey,
    async (req, res: CheckIsSurveyResponse, next) => {
      try {
        const { survey } = res.locals;
        const { utrvIds = [] } = mustValidate(
          req.body,
          z.object({
            utrvIds: z.string().array().optional(),
          })
        );
        const users = await getUtrvDelegationUsers().get(survey, utrvIds);

        res.FromModel(users);
      } catch (e) {
        next(e);
      }
    });

router.route('/:surveyId/report')
  .post(async (req, res, next) => {
    try {
      const survey = await Survey.findById(req.params.surveyId).populate('initiative').orFail().exec();
      const downloadScope = await DownloadScope.fromMultiScopeRequest(
        req,
        survey as SurveyWithInitiative,
        VisibilityStatus.ExcludeValuesOnly,
      );
      const data = await SurveyRepository.getByDownloadMultiScope(survey, downloadScope, { history: 1 })
      res.FromModel(data)
    } catch (e) {
      next(e)
    }
  });

router.route('/:surveyId/report/pptx').post(async (req, res, next) => {
  try {
    const { templateScheme, colorScheme, debug, useAISlideSuggestions } = mustValidate(req.body, pptxReportQuerySchema);

    const survey: SurveyModel & { initiative?: InitiativePlain } = await Survey.findById(req.params.surveyId)
      .populate('initiative')
      .orFail()
      .exec();
    if (!survey.initiative) {
      throw new UserError('Unable to fetch details of selected survey. If this continues please contact our support.');
    }

    const initiativeId = survey.initiativeId;

    const rootInitiativeService = getRootInitiativeService();
    const config = await rootInitiativeService.getConfig(survey.initiative, { domain: req.header('origin') });

    const isStaff = req.user?.isStaff;
    const hasAIFeature = rootInitiativeService.hasFeature(config, FeatureCode.PPTXReportAI);

    if (!hasAIFeature && !isStaff) {
      return res.NotPermitted();
    }

    const pptxService = getPPTXReportingService();

    const { surveyId } = req.params;

    const job = await pptxService.createJob({
      templateName: isCTStarter(survey.initiative) ? PPTXTemplateName.CTStarter : PPTXTemplateName.CT,
      templateScheme,
      colorScheme,
      type: TaskType.GenerateReportPPTX,
      initiativeId,
      surveyId: new ObjectId(surveyId),
      userId: req.user._id,
      downloadScope: undefined,
      debug: toBoolean(debug),
      useAISlideSuggestions: isStaff ? toBoolean(useAISlideSuggestions) : undefined,
    });
    res.FromModel({ jobId: job._id.toString(), status: job.status });
  } catch (e) {
    next(e);
  }
});

router.route('/:surveyId/background-report/:jobId/:taskId')
  .get(async (req, res, next) => {
    try {

      const survey: SurveyModel & { initiative?: InitiativePlain } = await Survey.findById(req.params.surveyId)
        .populate('initiative')
        .orFail()
        .exec();
      if (!survey.initiative) {
        throw new UserError('Unable to fetch details of selected survey. If this continues please contact our support.');
      }

      const rootInitiativeService = getRootInitiativeService();
      const config = await rootInitiativeService.getConfig(survey.initiative, { domain: req.header('origin') });

      const isAllowed = req.user?.isStaff || survey.type === SurveyType.Materiality;
      const hasAIFeature = rootInitiativeService.hasFeature(config, FeatureCode.PPTXReportAI);

      if (!hasAIFeature && !isAllowed) {
        return res.NotPermitted();
      }

      const { jobId, taskId } = req.params;
      const reportService = getGenerateReportService();
      const downloadUrl = await reportService.getDownloadUrl(new ObjectId(jobId), taskId);

      res.FromModel({
        downloadUrl
      });
    } catch (e) {
      next(e);
    }
  })
  .delete(async (req, res, next) => {
    try {
      const { initiativeId } = await Survey.findById(req.params.surveyId, { initiativeId: 1 }).orFail().exec();
      if (!(await InitiativePermissions.canManageInitiative(req.user, initiativeId))) {
        throw new PermissionDeniedError();
      }
      const { jobId, taskId } = req.params;
      await getPPTXReportManager().deleteReport({ initiativeId, jobId, taskId });
      res.Success(`Successfully delete pptx report with jobId=${jobId} and taskId=${taskId}`);
    } catch (e) {
      next(e);
    }
  });

router.route('/:surveyId/report/historical')
  .post(async (req, res, next) => {
    try {
      const survey = await Survey.findById(req.params.surveyId).populate('initiative').orFail().exec();
      const downloadScope = await DownloadScope.fromMultiScopeRequest(
        req,
        survey as SurveyWithInitiative,
        VisibilityStatus.ExcludeValuesOnly,
      );

      const data = await SurveyRepository.getHistoricalReportData({
        survey: survey,
        downloadScope: downloadScope,
        // Allow to load history. Even though we should remove and depend on utrv.notes property only
        reportProject: { history: 1, notes: 1 }
      });
      const [firstData] = data;

      const targets = req.body.targets ? await InitiativeRepository.getTargets({
        initiativeId: survey.initiativeId,
        // don't have universalTrackerId, use expanded property
        universalTrackerIds: firstData.reportData.map((r) => r.universalTracker?._id),
      }) : []

      res.FromModel({ historical: data, targets })
    } catch (e) {
      next(e)
    }
  });

router.route('/:surveyId/report/sdg')
  .get(async (req, res, next) => {
    try {
      const survey = await SurveyRepository.mustFindById(req.params.surveyId);
      await survey.populate('initiative');

      const downloadScope = await DownloadScope.fromRequestDoc(req as DownLoadRequestDoc, survey as SurveyWithInitiative);
      const data = await getReportService().getSDGReportData({
        survey,
        downloadScope,
        project: { notes: 1, history: 1 },
      })
      res.FromModel(data)
    } catch (e) {
      next(e)
    }
  });

router.route('/:surveyId/scope-wheels')
  .post(paramIdCheck(['surveyId']), canManageSurvey, async (req, res) => {
    try {
      const { survey } = res.locals;

      const validatePreferences: ScopeWheelPreferences[] = [];
      req.body.forEach((d: Partial<ScopeWheelPreferences>) => {
        if (d.scopeCode) {
          validatePreferences.push({
            scopeCode: d.scopeCode,
            visible: d.visible !== false
          });
        }
      });
      const result = await surveyManager.setScopeWheelPreferences(survey, validatePreferences);
      res.FromModel(result);
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/utr/:utrId').get(async (req, res) => {
  const utrId = new ObjectId(req.params.utrId);
  try {
    const surveys = await UtrSurveysService.getSurveys(utrId);

    return res.FromModel(surveys);
  } catch (e) {
    res.Exception(e);
  }
});

module.exports = router;
