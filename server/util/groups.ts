import { type Group } from '@g17eco/core';
import { type MetricGroupWithSubgroups } from '../models/metricGroup';

export const isMetricGroup = (group: Group | MetricGroupWithSubgroups): group is MetricGroupWithSubgroups => {
  return '_id' in group && 'groupName' in group;
};

export function getGroupCode(group: Group | MetricGroupWithSubgroups): string {
  return isMetricGroup(group) ? String(group._id) : group.code;
}

export const getSubGroup = <T extends Group | MetricGroupWithSubgroups>(group: T, code: string): T | undefined => {
  const subGroup = group.subgroups?.find((g) => getGroupCode(g) === code);
  if (!subGroup) {
    return;
  }

  return subGroup as T;
};

export const findGroupByChildCode = <T extends Group | MetricGroupWithSubgroups>(
  group: T,
  code: string
): T | undefined => {
  if (!group.subgroups) {
    return undefined;
  }

  for (const groupElement of group.subgroups) {
    if (getGroupCode(groupElement) === code || findGroupByChildCode(groupElement, code)) {
      return groupElement as T;
    }
  }
};

export const getGroupChildrenTags = <T extends Group | MetricGroupWithSubgroups>(group: T) => {
  const validCodes: string[] = [];
  if (!group.subgroups) {
    return validCodes;
  }

  for (const groupElement of group.subgroups) {
    validCodes.push(getGroupCode(groupElement), ...getGroupChildrenTags(groupElement));
  }

  return isMetricGroup(group) ? validCodes.map(id => ) : validCodes;
};
