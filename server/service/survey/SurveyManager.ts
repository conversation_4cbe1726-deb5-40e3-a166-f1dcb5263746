/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import Survey, {
  ScopeWheelPreferences,
  SurveyModel,
  SurveyModelPlain,
  SurveyPlainExtended,
  SurveyType,
} from '../../models/survey';
import { ObjectId } from 'bson';
import { wwgLogger } from '../wwgLogger';
import { Logger } from 'winston';
import {
  createGroupUpdate,
  createStakeholderGroup,
  StakeholderGroupManager,
  StakeholderGroupUpdate,
} from '../stakeholder/StakeholderGroupManager';
import UniversalTrackerValue, { UniversalTrackerValuePlain, utrvValidTypes } from '../../models/universalTrackerValue';
import UserError from '../../error/UserError';
import UniversalTrackerValueManager, { createUniversalTrackerValueManager } from '../utr/UniversalTrackerValueManager';
import { SurveyImporter } from './SurveyImporter';
import User, { UserModel, UserPlain } from '../../models/user';
import { OnboardingModelPlain, OnboardingWithSurveyConfig } from '../../models/onboarding';
import Initiative, { InitiativePlain } from '../../models/initiative';
import { buildSurveyUpdateMap, updateStakeholders } from '../stakeholder/StakeholderUtil';
import * as utrvUtil from '../utr/utrvUtil';
import { StakeholderGroup } from '../../models/stakeholderGroup';
import { createDelegationManager, DelegationManager } from '../delegation/DelegationManager';
import { createAssuranceFileBundler, UtrvFileBundler } from '../assurance/UtrvFileBundler';
import { getUploaderType, SupportedEvidenceTypes } from '../evidence/UploaderFactory';
import { createDownloadUrl } from '../storage/fileStorage';
import { DocumentOwnerType, DocumentPlain } from '../../models/document';
import { DocumentRepository } from '../../repository/DocumentRepository';
import { SurveyPermissions } from './SurveyPermissions';
import { blueprintDefaultUnitConfig, SupportedMeasureUnits } from '../units/unitTypes';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { createSurveyComposer, SurveyComposer } from './SurveyComposer';
import { VisibleStakeholders } from './VisibleStakeholders';
import { ScopeChangeRequest } from './model/DelegationScope';
import { processScopeChange } from './surveyDelegationProcess';
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';
import { DownloadScopeData } from './scope/downloadScope';
import { getMaterialityCode } from './scope/materiality';
import { Actions } from '../action/Actions';
import { extractVisibleUtrCodes } from '../../survey/surveyForms';
import UniversalTracker from '../../models/universalTracker';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { customDateFormat, DateFormat, toDate } from '../../util/date';
import moment from 'moment';
import { getGroup } from '@g17eco/core';
import BadRequestError from '../../error/BadRequestError';
import { getNotificationManager, NotificationManager } from '../notification/NotificationManager';
import { getRootInitiativeService, RootConfig, RootInitiativeService } from '../organization/RootInitiativeService';
import { DataPeriods } from '../utr/constants';
import { canAccessCustomScopeGroup } from '../payment/subscriptionCodes';
import { RequireAtLeastOne } from '../../util/type';
import { applyVisibilityFilter, applyVisibilityProject } from '../../repository/visibilityFilter';
import { PipelineStage } from 'mongoose';
import { UserForUtrManager } from '../utr/UniversalTrackerActionManager';
import { SurveyConflictVerification } from './SurveyConflictVerification';
import { SurveyDeadlineService, getSurveyDeadlineService } from './SurveyDeadline';
import { MetricGroupType, MetricGroupWithSubgroups } from '../../models/metricGroup';

class ResultUpdate {
  success = false;
  count = 0;
  failCount = 0;
  model?: SurveyModel;
  data?: any;

  public static fromResults(data: { success: boolean }[], model?: SurveyModel) {
    const result = new ResultUpdate();
    result.count = data.length;
    result.failCount = data.filter(u => !u.success).length;
    result.success = true;
    result.model = model;
    return result
  }
}

type Action = 'regenerate' | 'recalculate';

interface SurveyUpdateRequest {
  survey: SurveyModel;
  surveyUtrvIds: ObjectId[];
  stakeholderGroupUpdate: StakeholderGroupUpdate;
  verificationRequired?: boolean;
  evidenceRequired?: boolean;
  noteRequired?: boolean;
  isPrivate?: boolean;
  effectiveDate?: Date | string;
  period?: DataPeriods;
  type?: string;
}

type SurveyUpdateData = Partial<
  Pick<
    SurveyModelPlain,
    | 'evidenceRequired'
    | 'verificationRequired'
    | 'noteRequired'
    | 'isPrivate'
    | 'effectiveDate'
    | 'type'
    | 'unitConfig'
    | 'sourceName'
    | 'period'
    | 'name'
    | 'noteInstructions'
    | 'noteInstructionsEditorState'
  >
> & {
  deadlineDate?: string;
  scheduledDates?: {
    idempotencyKey?: string;
    date: string;
  }[];
};

interface Flags {
  evidenceRequired: boolean;
  verificationRequired: boolean;
  noteRequired: boolean;
  isPrivate: boolean;
}

export type FlagProperties = RequireAtLeastOne<Flags>

export interface SurveyUtrvsUpdates {
  properties: FlagProperties,
  utrvIds: string[]
}


export type SurveyUpdateProps = Pick<
  SurveyModelPlain,
  | 'name'
  | 'period'
  | 'evidenceRequired'
  | 'noteRequired'
  | 'verificationRequired'
  | 'isPrivate'
  | 'sourceName'
  | 'unitConfig'
  | 'noteInstructions'
  | 'noteInstructionsEditorState'
  | 'deadlineDate'
  | 'scheduledDates'
> & { toObject?: () => SurveyUpdateProps };

export class SurveyManager {

  constructor(
    private logger: Logger,
    private utrvManager: UniversalTrackerValueManager,
    private delegationManager: DelegationManager,
    private bundler: UtrvFileBundler,
    private docRepo: typeof DocumentRepository,
    private surveyComposer: SurveyComposer,
    private notificationManager: NotificationManager,
    private rootInitiativeService: RootInitiativeService,
    private surveyDeadlineService: SurveyDeadlineService
  ) {
  }

  public async updateStakeholderGroup(
    survey: SurveyModel,
    stakeholderGroupUpdate: StakeholderGroupUpdate,
    evidenceRequired?: boolean,
    noteRequired?: boolean,
    verificationRequired?: boolean,
    type?: string,
  ) {

    survey.stakeholders = StakeholderGroupManager.mergeGroup(survey.stakeholders, stakeholderGroupUpdate);
    const surveyUtrvIds = this.getSurveyAllUtrvIds(survey);

    this.logger.info(`Updating survey ${survey._id.toString()} stakeholder group to ${surveyUtrvIds.length} utrv's`);

    const result = await this.updateUtrvGroup({
      survey,
      surveyUtrvIds,
      stakeholderGroupUpdate,
      verificationRequired,
      evidenceRequired,
      noteRequired,
      type
    });
    this.logger.info('Received update utrv group result: %o', result);

    // Must happen after removing utrv reference for VisibleStakeholders array to properly update
    survey.visibleStakeholders = await VisibleStakeholders.update(survey, stakeholderGroupUpdate);

    return { ...result, model: await survey.save() };
  }

  private getSurveyAllUtrvIds(survey: SurveyModel) {
    return survey.compositeUtrvs.concat(
      survey.fragmentUtrvs,
      survey.subFragmentUtrvs,
      survey.visibleUtrvs, // Assume all visibleUtrvs are owned by this survey
      survey.disabledUtrvs,
    );
  }

  private async updateUtrvGroup({
    survey,
    surveyUtrvIds,
    stakeholderGroupUpdate,
    verificationRequired,
    evidenceRequired,
    noteRequired,
    isPrivate,
    type,
    effectiveDate,
    period
  }: SurveyUpdateRequest) {

    const result = new ResultUpdate();

    if (type && !utrvValidTypes.includes(type)) {
      throw new Error(`Trying to update utrv type to invalid type ${type}`)
    }

    const utrvs = await UniversalTrackerValue
      .find({ _id: { $in: surveyUtrvIds } }).exec();

    const sourceName = survey.sourceName;
    const visibleUtrvs = new Set(survey.visibleUtrvs.map(String));

    const toUpdateUtrvs = utrvs.map(utrv => {

      // shouldIgnoreFlags, but not visible utrvs
      // Visible utrvs will be submitted directly, flags should match survey
      const shouldIgnoreFlags = utrvUtil.isComposite(utrv, sourceName)
        && !visibleUtrvs.has(utrv._id.toString());

      utrv.stakeholders = StakeholderGroupManager.mergeGroup(utrv.stakeholders as StakeholderGroup, stakeholderGroupUpdate);
      if (typeof evidenceRequired === 'boolean') {
        utrv.evidenceRequired = shouldIgnoreFlags ? false : evidenceRequired;
      }
      if (typeof noteRequired === 'boolean') {
        utrv.noteRequired = shouldIgnoreFlags ? false : noteRequired;
      }
      if (typeof verificationRequired === 'boolean') {
        utrv.verificationRequired = shouldIgnoreFlags ? false : verificationRequired;
      }
      if (typeof isPrivate === 'boolean') {
        utrv.isPrivate = shouldIgnoreFlags ? false : isPrivate;
      }

      if (type) {
        utrv.type = type;
      }

      if (period) {
        utrv.period = period;
      }

      if (effectiveDate) {
        utrv.effectiveDate = moment(effectiveDate).toDate();
      }

      return utrv;
    });

    const updateResult = await UniversalTrackerValue.bulkSave(toUpdateUtrvs);

    result.count = toUpdateUtrvs.length;
    result.failCount = updateResult.getWriteErrorCount();
    result.success = true;

    return result;
  }

  public async setScopeWheelPreferences(survey: SurveyModel, scopeWheels: ScopeWheelPreferences[]) {
    if (!survey) {
      return false;
    }

    if (!survey.displayPreferences) {
      survey.displayPreferences = {};
    }
    survey.displayPreferences.scopeWheels = scopeWheels;
    return survey.save();
  }

  public async updateSettings(survey: SurveyModel, user: UserModel, data: SurveyUpdateData) {
    if (!survey || typeof data !== 'object') {
      return false;
    }

    const isCompleted = Boolean(survey.completedDate);
    if (isCompleted) {
      return false; // Not allowed to change settings if survey is marked as completed
    }

    const hasPermissions = await SurveyPermissions.canManage(survey, user);
    if (!hasPermissions) {
      return false;
    }

    const whitelistData: Partial<SurveyUpdateProps> = {
      name: data.name ?? survey.name,
      period: data.period ?? survey.period,
      evidenceRequired: data.evidenceRequired ?? survey.evidenceRequired,
      noteRequired: data.noteRequired ?? survey.noteRequired,
      verificationRequired: data.verificationRequired ?? survey.verificationRequired,
      isPrivate: data.isPrivate ?? survey.isPrivate,
      sourceName: data.sourceName ?? survey.sourceName,
    };
    if (data.noteInstructions !== undefined) {
      whitelistData.noteInstructions = data.noteInstructions
    }

    if (data.noteInstructionsEditorState !== undefined) {
      whitelistData.noteInstructionsEditorState = data.noteInstructionsEditorState
    }

    if (data.unitConfig !== undefined) {
      whitelistData.unitConfig = data.unitConfig;
    }

    whitelistData.deadlineDate = data.deadlineDate ? toDate(data.deadlineDate) : undefined;
    whitelistData.scheduledDates = this.surveyDeadlineService.processScheduledDates(data);

    // create deadline reminder
    if (survey.deadlineDate && !data.deadlineDate) {
      await this.surveyDeadlineService.deleteAllNotifications(survey);
    } else if (data.deadlineDate && this.surveyDeadlineService.validate(whitelistData)) {

      // create new scheduled notifications
      const newScheduledDates = (whitelistData.scheduledDates ?? []).filter(item => !item.idempotencyKey);
      const createdScheduledDates = await this.surveyDeadlineService.bulkCreate({ survey, scheduledDates: newScheduledDates });

      // update scheduled notifications
      const updateScheduledDates = (whitelistData.scheduledDates ?? []).filter(item => item.idempotencyKey);
      await this.surveyDeadlineService.bulkUpdate({ survey, updateScheduledDates: updateScheduledDates });

      whitelistData.scheduledDates = this.surveyDeadlineService.sortScheduledDates([
        ...updateScheduledDates,
        ...createdScheduledDates,
      ]);
    }

    const effectiveDate = await this.getNewEffectiveDate(survey, data.effectiveDate);

    survey.set({ ...whitelistData, effectiveDate });
    await SurveyConflictVerification.verify(survey, user);

    if (['evidenceRequired', 'noteRequired', 'verificationRequired', 'isPrivate', 'effectiveDate', 'period'].some(f => survey.isModified(f))) {
      await this.updateUtrvGroup({
        survey: survey,
        surveyUtrvIds: this.getSurveyAllUtrvIds(survey),
        stakeholderGroupUpdate: {
          add: { stakeholder: [], verifier: [], escalation: [] },
          remove: { stakeholder: [], verifier: [], escalation: [] },
        },
        verificationRequired: whitelistData.verificationRequired,
        evidenceRequired: whitelistData.evidenceRequired,
        noteRequired: whitelistData.noteRequired,
        isPrivate: whitelistData.isPrivate,
        effectiveDate: data.effectiveDate,
        period: data.period
      });
    }

    if (survey.isModified('unitConfig')) {
      await this.updateCurrency(survey);
    }

    if (survey.isModified()) {
      wwgLogger.info(`User ${user._id} updated survey ${survey._id} unit config`)
    }

    await survey.save();

    return survey;
  }

  public async updateUtrvSettings(survey: SurveyModel, user: UserModel, data: SurveyUtrvsUpdates) {
    const isCompleted = Boolean(survey.completedDate);
    if (isCompleted) {
      return false; // Not allowed to change settings if survey is marked as completed. Don't know if required here, but seems sensible
    }

    const hasPermissions = await SurveyPermissions.canManage(survey, user);
    if (!hasPermissions) {
      return false;
    }

    const { properties, utrvIds } = data;

    const whitelistData: Partial<SurveyModelPlain> = {};
    if (properties.evidenceRequired !== undefined) {
      whitelistData.evidenceRequired = properties.evidenceRequired;
    }

    if (properties.verificationRequired !== undefined) {
      whitelistData.verificationRequired = properties.verificationRequired;
    }

    if (properties.noteRequired !== undefined) {
      whitelistData.noteRequired = properties.noteRequired;
    }

    if (properties.isPrivate !== undefined) {
      whitelistData.isPrivate = properties.isPrivate;
    }

    if (!Object.values(whitelistData).some(v => v !== undefined)) {
      return false; // Nothing to update
    }

    const validIds = new Set(this.getSurveyAllUtrvIds(survey).map(String));
    const updateIds = utrvIds.filter(s => validIds.has(s)).map(s => new ObjectId(s));

    if (updateIds.length === 0) {
      return false;
    }

    return UniversalTrackerValue.updateMany(
      { _id: { $in: updateIds } },
      { $set: whitelistData },
      { multi: true },
    );
  }

  private async getNewEffectiveDate(survey: SurveyModel, effectiveDate?: Date | string): Promise<Date> {
    const hasChanged = this.shouldUpdateEffectiveDate({
      current: survey.effectiveDate,
      newDate: effectiveDate
    });

    if (hasChanged) {
      const newDate = moment(effectiveDate).toDate();
      return newDate;
    }

    return survey.effectiveDate;
  }

  private async updateCurrency(survey: SurveyModel) {

    const currencyUnit = survey.unitConfig?.currency;
    if (!currencyUnit) {
      return;
    }

    const utrvs = await UniversalTrackerValue.find({
      _id: {
        $in: this.getSurveyAllUtrvIds(survey),
      },
    }).populate('universalTracker').exec();

    const currencyUtrvs = utrvs.filter(({ universalTracker }) => {
      return universalTracker?.unitType === SupportedMeasureUnits.currency;
    }).map(utrv => {
      utrv.unit = currencyUnit;
      if (utrv.valueData?.input?.unit) {
        utrv.valueData.input.unit = currencyUnit;
      }
      return utrv.save();
    });

    return Promise.all(currencyUtrvs);
  }

  public async processAction(survey: SurveyModel, user: UserPlain, action: Action) {
    switch (action) {
      case 'recalculate':
        return this.recalculate(survey, user);
      case 'regenerate':
        return this.regenerate(survey, user);
      default:
        throw new UserError(`Survey action ${action} is not supported`);
    }
  }

  public async regenerate(survey: SurveyModel, user: UserPlain, filterUtrCodes?: string[]) {
    survey.blueprint = await this.surveyComposer.composeBlueprint(survey, filterUtrCodes);
    await survey.save();
    return SurveyImporter.regenerate(user, survey);
  }

  private async recalculate(survey: SurveyModel, user: UserForUtrManager) {
    return this.utrvManager.recalculateFragmentParents(survey.visibleUtrvs, user);
  }

  public async onboardExistingSurveys(onboarding: OnboardingModelPlain, user: UserModel) {
    const { ids, groupUpdate } = buildSurveyUpdateMap(onboarding, user);
    const surveys = await Survey.find({ _id: { $in: ids } }).exec();

    const updates = surveys.map(survey => {

      // Apply updates as groupUpdate
      return this.updateStakeholderGroup(survey, {
        add: groupUpdate.get(survey._id.toString()) ?? createStakeholderGroup(),
        remove: createStakeholderGroup(),
      });
    });

    return Promise.all(updates);
  }

  public async onboardSurveyUser(onboarding: OnboardingWithSurveyConfig, user: UserPlain) {
    const { surveyId, complete, ...data } = onboarding.surveyConfig;

    const addGroupUpdate: StakeholderGroupUpdate = {
      add: { stakeholder: [user._id], verifier: [], escalation: [] },
      remove: createStakeholderGroup()
    };

    const survey = await SurveyImporter.getSurveyCodeOrId(data.code, surveyId);
    if (survey) {
      const update = await this.updateStakeholderGroup(survey, addGroupUpdate);
      return update.model;
    }

    // Create new one
    const initiative = await Initiative.findById(onboarding.initiativeId).lean().exec();
    if (!initiative) {
      throw new Error(`Failed to find initiative by id ${onboarding.initiativeId}`)
    }

    const name = `${initiative.name} - ${data.utrvType} ${customDateFormat(data.effectiveDate, DateFormat.Slash, false)}`;

    const newSurvey = await SurveyImporter.create(
      {
        ...data,
        type: data.type ?? SurveyType.Default,
        name,
        visibleUtrvs: [],
        visibleStakeholders: [],
        unitConfig: blueprintDefaultUnitConfig,
        initiativeId: onboarding.initiativeId,
        stakeholders: createStakeholderGroup(),
        roles: { admin: [user._id], viewer: [] },
      },
      user
    );

    this.logger.info(`New onboarding survey ${data.code} have been created`);
    const newSurveyUpdate = await this.updateStakeholderGroup(newSurvey, addGroupUpdate);
    return newSurveyUpdate.model;
  }

  private async manageUserUtrv(user: UserPlain, survey: SurveyModel, type: keyof StakeholderGroup, action: 'add' | 'remove') {
    const update: any = {
      add: { stakeholder: [], verifier: [], escalation: [] },
      remove: { stakeholder: [], verifier: [], escalation: [] }
    };
    update[action] = updateStakeholders(type, user._id);
    await this.updateStakeholderGroup(survey, update);

    await survey.save();
    return true;
  }

  public async delegationAction(
    surveyId: string,
    userId: string,
    type: keyof StakeholderGroup,
    action: Actions,
    delegator: UserModel
  ): Promise<Boolean> {
    const [user, survey] = await Promise.all([
      User.findById(userId).lean().orFail().exec(),
      Survey.findById(surveyId).orFail().exec(),
    ]);
    await this.delegationManager.checkSurvey({ survey, user: delegator, type });
    const result = await this.manageUserUtrv(user, survey, type, action);
    const utrv = survey.visibleUtrvs.length
      ? await UniversalTrackerValueRepository.mustFindById(survey.visibleUtrvs[0])
      : undefined;

    this.notificationManager.sendBulkDelegation({
      survey,
      questionCount: survey.visibleUtrvs.length,
      userId,
      action,
      delegator,
      utrv,
    }).catch(wwgLogger.error)

    return result;
  }

  public async download(survey: SurveyPlainExtended, user: UserPlain, downloadScope?: DownloadScopeData) {

    const subType = this.getSubType(downloadScope);
    const utrvs = await this.getDownloadUtrvs(survey, downloadScope);

    const utrvHistorySum = utrvs.reduce((a, c) => a + c.history.length, 0);

    // get latest download
    const existingDocs = await this.getBundleDocuments(survey, utrvHistorySum, subType);
    if (existingDocs.length > 0) {
      return existingDocs;
    }

    const filenameType = downloadScope?.values?.join(' ').toUpperCase();

    const ids = utrvs.reduce<any>((a, c) => {
      a.push(c._id);
      if (c.compositeData?.fragmentUtrvs) {
        a.push(...c.compositeData.fragmentUtrvs)
      }
      return a;
    }, [])
    const bundle = await this.bundler.createSurveyBundle({
      survey,
      ids,
      user,
      preferredTypes: downloadScope?.values ?? [],
      filenameType,
    })

    bundle.ownerId = survey._id;
    bundle.checksum = String(utrvHistorySum);
    bundle.ownerType = DocumentOwnerType.Survey;
    bundle.ownerSubType = subType;
    await bundle.save();

    const bundleDoc = await createDownloadUrl(bundle.toObject())
    return [bundleDoc];
  }

  public getSubType(downloadScope?: DownloadScopeData) {
    return downloadScope ? [downloadScope.type, ...(downloadScope.values ?? [])].join('_') : undefined;
  }

  public async getDownloadUtrvs(survey: SurveyPlainExtended, downloadScope?: DownloadScopeData): Promise<UniversalTrackerValuePlain[]> {

    const projection = {
      _id: 1,
      history: 1,
      compositeData: 1,
    } as const;

    //includes sdg contribution matching
    if (downloadScope?.type === 'standards') {
      return UniversalTrackerValueRepository.getUtrvsByIdsAndUtrType(
        survey,
        downloadScope,
        projection,
      );
    }

    if (downloadScope?.type === 'frameworks' && downloadScope.values) {
      const matchOr = {
        $or: downloadScope?.values?.map(t => {
          return { [`universalTracker.tags.${t}`]: { $exists: true, $ne: [] } }
        })
      }
      return SurveyRepository.getSurveyQuestionByMatch(
        survey,
        downloadScope,
        matchOr,
        projection,
      );
    }

    const visibility = downloadScope?.visibilityStatus;
    const pipeline: PipelineStage[] = [
      {
        $match: applyVisibilityFilter({ _id: { $in: survey.visibleUtrvs } }, visibility)
      },
      { $project: applyVisibilityProject(projection, visibility) }
    ];

    return UniversalTrackerValue.aggregate(pipeline).exec();
  }

  private async getBundleDocuments(survey: SurveyModelPlain, utrvHistorySum: number, subType?: string): Promise<DocumentPlain[]> {

    const bundleDocs = await this.docRepo.findOwner(survey._id, DocumentOwnerType.Survey, subType);
    const bundle = bundleDocs.pop();

    if (!bundle || bundle.checksum !== String(utrvHistorySum)) {
      return [];
    }

    const bundleDoc = await createDownloadUrl(bundle)
    return [bundleDoc];
  }

  private getUpdateIds(group: StakeholderGroup) {
    return new Set([
      ...group.stakeholder,
      ...group.verifier,
      ...group.escalation
    ].map(String))
  }

  public async updateUtrvStakeholderChange(utrvId: ObjectId, update: StakeholderGroupUpdate) {

    const add = this.getUpdateIds(update.add);
    if (add.size > 0) {
      const surveys = await Survey.find({
        visibleUtrvs: utrvId,
        visibleStakeholders: { $nin: Array.from(add).map(id => new ObjectId(id)) }
      });

      return Promise.all(surveys.map(async survey => {
        survey.visibleStakeholders = await VisibleStakeholders.update(survey, update);
        return survey.save();
      }))
    }

    const { escalation, stakeholder, verifier } = update.remove;
    const removeIds = [...stakeholder, ...verifier, ...escalation];

    if (removeIds.length > 0) {
      const surveys = await Survey.find({
        visibleUtrvs: utrvId,
        visibleStakeholders: { $in: removeIds }
      });

      return Promise.all(surveys.map(async survey => {
        const remainingUtrvs = await UniversalTrackerValue
          .find({
            _id: { $in: survey.visibleUtrvs },
            $or: [
              { 'stakeholders.stakeholder': { $in: removeIds } },
              { 'stakeholders.verifier': { $in: removeIds } },
            ]
          }).countDocuments().exec()

        if (remainingUtrvs === 0) {
          survey.visibleStakeholders = await VisibleStakeholders.update(survey, update);
          await survey.save();
        }

        return true;
      }))
    }
  }

  public async regenerateBlueprintById(surveyId: string, user: UserPlain) {
    const survey = await Survey.findById(surveyId).orFail().exec();
    return this.regenerateBlueprint(survey, user);
  }

  public async regenerateBlueprint(survey: SurveyModel, user: UserPlain) {
    survey.blueprint = await this.surveyComposer.composeBlueprint(survey);
    survey.ignoredDate = undefined;
    await survey.save();
    await SurveyImporter.regenerate(user, survey);
    this.logger.info(`Updated survey (${survey._id}) blueprint`, {
      metricGroupIds: survey.scope?.custom.join(',') || '',
      surveyId: String(survey._id),
    });
  }

  public async ignoreMetricGroupChanges(surveyId: string) {
    const survey = await Survey.findOne({ _id: new ObjectId(surveyId) }).exec();
    if (!survey) {
      wwgLogger.error(`Failed to find survey by ${surveyId} while trying to ignore survey change`);
      return;
    }
    survey.ignoredDate = new Date();
    await survey.save();
    this.logger.info(`Ignored survey (${survey._id}) blueprint update`, {
      metricGroupId: survey.scope?.custom.join(',') || '',
      surveyId: String(survey._id),
    });
  }

  validateScopeChange(request: ScopeChangeRequest, config: RootConfig) {
    const { scopeGroups, action } = request;
    if (action === Actions.Add) {
      scopeGroups.forEach(scopeGroup => {
        if (scopeGroup.scopeType !== 'custom') {
          const group = getGroup(scopeGroup.scopeType, scopeGroup.code)
          if (group && !canAccessCustomScopeGroup(group.requiredTags, config.survey.scope)) {
            throw new BadRequestError(`You can not add: ${group.name}`)
          }
        }
      })
    }
  }

  public async updateSurveyScope(request: ScopeChangeRequest) {

    const { delegator: user, surveyId } = request;
    const survey = await SurveyRepository.mustFindById(surveyId);

    const hasPermissions = await SurveyPermissions.canManage(survey, user);
    if (!hasPermissions) {
      throw new UserError(`User does not have permissions to update scope`);
    }

    const initiativeId = String(survey.initiativeId);
    const initiative = await InitiativeRepository.getInitiativeById(initiativeId);

    if (!initiative) {
      throw new Error(`Failed to find initiative with "${initiativeId}"`);
    }

    const config = await this.rootInitiativeService.getConfig(initiative, { domain: request.domain });

    //Check for requireTags permission before updating scope
    this.validateScopeChange(request, config);

    // fetch metric groups
    let metricGroups: MetricGroupWithSubgroups[] = [];
    const metricGroupIds = request.scopeGroups.filter((g) => g.scopeType === 'custom').map((g) => g.code);
    if (metricGroupIds.length > 0) {
      metricGroups = await InitiativeRepository.getInitiativeKpiGroups(initiativeId, MetricGroupType.Custom, metricGroupIds);
    }

    if (survey.scope && Number(survey.scope.materiality?.length) > 0) {
      // Convert materiality to SDG
      const materialitySdgCodes = getMaterialityCode(initiative, survey.scope.materiality);

      // Apply existing materiality as SDG codes
      survey.scope = processScopeChange({
        data: {
          action: Actions.Add,
          scopeGroups: materialitySdgCodes.map((code) => ({ code: code, scopeType: 'sdg' })),
        },
        surveyScope: survey.scope,
      });
      survey.scope.materiality = []; // Reset now
    }

    survey.scope = processScopeChange({ data: request, surveyScope: survey.scope, metricGroups });

    // Occasionally a survey might not have one (old?), so compose one
    const blueprint = survey.blueprint ?? await this.surveyComposer.composeBlueprint(survey);
    const utrCodes = extractVisibleUtrCodes(blueprint);
    await this.regenerate(survey, user);

    // Re-calculate affected utrvs
    const currentCodes = extractVisibleUtrCodes(blueprint);
    const { add, remove } = createGroupUpdate(utrCodes, currentCodes);
    await this.triggerBlueprintChangeCalculation(survey, user, add, remove);

    return survey;
  }

  private async triggerBlueprintChangeCalculation(survey: SurveyModel, user: UserModel, add: string[], remove: string[]) {
    const utrs = await UniversalTracker.find(
      { code: { $in: [...add, ...remove] } },
      { _id: 1 },
    ).lean().exec();

    const utrvs = await UniversalTrackerValueRepository.findWithDeleted({
      universalTrackerId: { $in: utrs.map(u => u._id) },
      'compositeData.surveyId': survey._id,
      initiativeId: survey.initiativeId,
    });

    const meta = {
      initiativeId: survey.initiativeId,
      surveyId: survey._id,
      userId: user._id,
    };

    this.logger.info(`Added UTR ${add.length}, removed ${remove.length}. Found ${utrvs.length}, recalculating`, meta);

    const updateResult = await this.utrvManager.recalculateFragmentParents(utrvs.map(v => v._id), user);
    const result = new ResultUpdate();
    result.count = updateResult.length;
    result.data = updateResult;
    result.success = true;
    this.logger.info(`Recalculate single source blueprint, updates of ${result.count}`, meta);

    return result;
  }

  public async getSurveyConfiguration(survey: SurveyModel) {
    if (!survey.populated('initiative')) {
      await survey.populate('initiative')
    }

    return {
      _id: survey._id,
      name: survey.name,
      period: survey.period,
      sourceName: survey.sourceName,
      effectiveDate: survey.effectiveDate,
      evidenceRequired: survey.evidenceRequired,
      noteRequired: survey.noteRequired,
      verificationRequired: survey.verificationRequired,
      isPrivate: survey.isPrivate,
      initiative: this.getMinInitiativeData(survey.initiative),
      unitConfig: survey.unitConfig,
      noteInstructions: survey.noteInstructions,
      noteInstructionsEditorState: survey.noteInstructionsEditorState,
      deadlineDate: survey.deadlineDate,
      scheduledDates: survey.scheduledDates,
      filters: survey.filters,
    }
  }

  private getMinInitiativeData(initiative?: InitiativePlain) {
    if (!initiative) {
      return;
    }
    return {
      name: initiative.name,
      code: initiative.code,
      _id: initiative._id,
      profile: initiative.profile,
    };
  }

  private shouldUpdateEffectiveDate(check: { current: string | Date, newDate?: string | Date }) {

    const { current, newDate: n } = check;

    if (!n) {
      return false;
    }
    const newDate = moment(n);
    const existing = moment(current);

    if (newDate.year() !== existing.year()) {
      return true;
    }

    return newDate.month() !== existing.month();
  }

  public async getUniquePeriods(initiativeId: string | ObjectId, surveyType: SurveyType) {
    const initiativeTree = await InitiativeRepository.getAllChildrenById(initiativeId);
    const initiativeIdArray = initiativeTree.map((initiative: InitiativePlain<ObjectId>) => initiative._id);
    return SurveyRepository.getInitiativeSurveyPeriods(initiativeIdArray, surveyType);
  }
}

export const createSurveyManager = (
  logger: Logger = wwgLogger,
  utrvManager: UniversalTrackerValueManager = createUniversalTrackerValueManager(),
  delegationManager: DelegationManager = createDelegationManager(),
) => {
  const uploader = getUploaderType(SupportedEvidenceTypes.SurveyBundle)
  return new SurveyManager(
    logger,
    utrvManager,
    delegationManager,
    createAssuranceFileBundler(uploader),
    DocumentRepository,
    createSurveyComposer(),
    getNotificationManager(),
    getRootInitiativeService(),
    getSurveyDeadlineService()
  );
};


let instance: SurveyManager;
export const getSurveyManager = () => {
  if (!instance) {
    instance = createSurveyManager();
  }

  return instance;
};
