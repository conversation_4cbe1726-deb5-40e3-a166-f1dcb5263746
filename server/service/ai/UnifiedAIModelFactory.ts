/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import type { <PERSON><PERSON> } from 'winston';
import { wwgLogger } from '../wwgLogger';
import { getClaudeAI } from './models/ClaudeAI';
import { getChatGPT, OPENAI_MODEL } from './models/ChatGPT';
import { getGeminiAI } from './models/GeminiAI';
import type { AIModel } from './models/AIModel';
import type { FileSupportAiModelWithCapabilities } from './models/FileSupportAiModel';
import ContextError from '../../error/ContextError';
import { BASE_SYSTEM_PROMPTS, PROMPT_PREFIXES } from './constants/basePrompts';

let instance: UnifiedAIModelFactory | undefined;

// Model configurations with provider mapping and cost information
// Cost is per 1M tokens in USD
export const UNIFIED_AI_MODELS = {
  // OpenAI models - GPT-5 series
  'gpt-5': { provider: 'openai', model: 'gpt-5', cost: { input: 15.00, output: 60.00 } },
  'gpt-5-mini': { provider: 'openai', model: 'gpt-5-mini', cost: { input: 0.15, output: 0.60 } },
  'gpt-5-nano': { provider: 'openai', model: 'gpt-5-nano', cost: { input: 0.05, output: 0.20 } },

  // GPT-4 series
  'gpt-4': { provider: 'openai', model: 'gpt-4', cost: { input: 30.00, output: 60.00 } },
  'gpt-4-turbo': { provider: 'openai', model: 'gpt-4-turbo-preview', cost: { input: 10.00, output: 30.00 } },
  'gpt-4o': { provider: 'openai', model: 'gpt-4o', cost: { input: 5.00, output: 15.00 } },
  'gpt-4.1-2025-04-14': { provider: 'openai', model: 'gpt-4.1-2025-04-14', cost: { input: 2.50, output: 10.00 } },

  // Claude models - latest versions and Claude 4
  'claude-3-5-sonnet-latest': { provider: 'claude', model: 'claude-3-5-sonnet-20241022', cost: { input: 3.00, output: 15.00 } },
  'claude-3-5-haiku-latest': { provider: 'claude', model: 'claude-3-5-haiku-20241022', cost: { input: 0.25, output: 1.25 } },
  'claude-sonnet-4-20250514': { provider: 'claude', model: 'claude-sonnet-4-20250514', cost: { input: 3.00, output: 15.00 } },
  'claude-opus-4-20250514': { provider: 'claude', model: 'claude-opus-4-20250514', cost: { input: 15.00, output: 75.00 } },

  // Gemini models - only latest 2.5 versions
  'gemini-2.5-pro': { provider: 'gemini', model: 'gemini-2.5-pro', cost: { input: 1.25, output: 5.00 } },
  'gemini-2.5-flash': { provider: 'gemini', model: 'gemini-2.5-flash', cost: { input: 0.075, output: 0.30 } },
} as const;

export const DEFAULT_TESTING_MODEL = 'claude-sonnet-4-20250514' satisfies keyof typeof UNIFIED_AI_MODELS;

export type UnifiedModelName = keyof typeof UNIFIED_AI_MODELS;

export interface ModelCapabilities {
  supportsFileOperations: boolean;
  supportsStructuredOutput: boolean;
  maxInputTokens: number;
  maxOutputTokens: number;
  costPerMillionTokens?: {
    input: number;
    output: number;
  };
}

export interface AIModelGetters {
  getChatGPT: typeof getChatGPT;
  getClaudeAI: typeof getClaudeAI;
  getGeminiAI: typeof getGeminiAI;
}

export class UnifiedAIModelFactory {
  // Lazy-loaded AI model instances
  private chatgpt?: ReturnType<typeof getChatGPT>;
  private claude?: ReturnType<typeof getClaudeAI>;
  private gemini?: ReturnType<typeof getGeminiAI>;

  private readonly getters: AIModelGetters;

  private readonly defaultModel = DEFAULT_TESTING_MODEL;

  constructor(
    protected logger: Logger,
    getters?: Partial<AIModelGetters>
  ) {
    // Allow dependency injection for testing
    this.getters = {
      getChatGPT: getters?.getChatGPT || getChatGPT,
      getClaudeAI: getters?.getClaudeAI || getClaudeAI,
      getGeminiAI: getters?.getGeminiAI || getGeminiAI
    };
  }

  /**
   * Get or create ChatGPT instance
   */
  private getChatGPTInstance(): ReturnType<typeof getChatGPT> {
    if (!this.chatgpt) {
      this.chatgpt = this.getters.getChatGPT();
    }
    return this.chatgpt;
  }

  /**
   * Get or create ClaudeAI instance
   */
  private getClaudeInstance(): ReturnType<typeof getClaudeAI> {
    if (!this.claude) {
      this.claude = this.getters.getClaudeAI();
    }
    return this.claude;
  }

  /**
   * Get or create GeminiAI instance
   */
  private getGeminiInstance(): ReturnType<typeof getGeminiAI> {
    if (!this.gemini) {
      this.gemini = this.getters.getGeminiAI();
    }
    return this.gemini;
  }

  /**
   * Get AI model instance by model name
   * @param modelName - The model name like 'gpt-4', 'claude-3-5-sonnet', 'gemini-1.5-pro'
   * @returns AI model instance
   */
  getModel(modelName: string = OPENAI_MODEL): AIModel {
    const config = UNIFIED_AI_MODELS[modelName as UnifiedModelName];

    if (!config) {
      this.logger.warn(`Unknown model "${modelName}", defaulting to ${OPENAI_MODEL}`);
      return this.getChatGPTInstance();
    }

    switch (config.provider) {
      case 'openai':
        // For now, all OpenAI requests use the same instance
        // TODO: In the future, we can make ChatGPT accept dynamic model selection
        return this.getChatGPTInstance();

      case 'claude':
        // For now, all Claude requests use the same instance
        // TODO: In the future, we can make ClaudeAI accept dynamic model selection
        return this.getClaudeInstance();

      case 'gemini':
        // All Gemini requests use the same instance
        return this.getGeminiInstance();

      default:
        this.logger.warn(`Unknown provider for model "${modelName}", defaulting to ${OPENAI_MODEL}`);
        return this.getChatGPTInstance();
    }
  }

  /**
   * Get AI model that supports file operations
   * @param modelName - The model name
   * @returns FileSupportAiModelWithCapabilities instance
   * @throws Error if model doesn't support file operations
   */
  public getFileSupportModel(modelName: string): FileSupportAiModelWithCapabilities {
    const capabilities = this.getModelCapabilities(modelName);

    if (!capabilities.supportsFileOperations) {
      throw new ContextError(`Model ${modelName} does not support file operations. Please use an OpenAI or Claude model.`);
    }

    const config = UNIFIED_AI_MODELS[modelName as UnifiedModelName];
    if (!config) {
      throw new ContextError(`Unknown model "${modelName}"`);
    }

    switch (config.provider) {
      case 'claude':
        return this.getClaudeInstance();
      case 'gemini':
        return this.getGeminiInstance();
      case 'openai':
        return this.getChatGPTInstance();
      default:
        throw new ContextError(`Unknown provider for model "${modelName}"`);
    }
  }

  /**
   * Get model capabilities
   */
  getModelCapabilities(modelName: string): ModelCapabilities {
    const config = UNIFIED_AI_MODELS[modelName as UnifiedModelName];

    if (!config) {
      // Default to OpenAI capabilities
      return {
        supportsFileOperations: true,
        supportsStructuredOutput: true,
        maxInputTokens: 128000,
        maxOutputTokens: 4096,
      };
    }

    switch (config.provider) {
      case 'openai':
        // GPT-5 series has enhanced capabilities
        if (modelName === 'gpt-5' || modelName === 'gpt-5-mini' || modelName === 'gpt-5-nano') {
          return {
            supportsFileOperations: true,
            supportsStructuredOutput: true,
            maxInputTokens: 272000,  // 272K input tokens as per documentation
            maxOutputTokens: 128000,  // 128K output tokens
            costPerMillionTokens: {
              input: modelName === 'gpt-5' ? 1.25 : modelName === 'gpt-5-mini' ? 0.75 : 0.5,  // Based on GPT-5 pricing
              output: modelName === 'gpt-5' ? 10.0 : modelName === 'gpt-5-mini' ? 5.0 : 2.5,
            },
          };
        }
        return {
          supportsFileOperations: true,
          supportsStructuredOutput: true,
          maxInputTokens: 128000,
          maxOutputTokens: modelName.includes('gpt-4') ? 4096 : 16384,
          costPerMillionTokens: {
            input: modelName.includes('gpt-4') ? 10 : 0.5,
            output: modelName.includes('gpt-4') ? 30 : 1.5,
          },
        };

      case 'claude':
        // Support both latest models and Claude 4
        if (!modelName.includes('latest') && !modelName.includes('claude-sonnet-4') && !modelName.includes('claude-opus-4')) {
          throw new ContextError('Only Claude 3.5 latest models and Claude 4 are supported. Please use claude-3-5-sonnet-latest, claude-3-5-haiku-latest, claude-sonnet-4-20250514, or claude-opus-4-20250514');
        }

        // Claude 4 has different capabilities
        if (modelName.includes('claude-sonnet-4') || modelName.includes('claude-opus-4')) {
          return {
            supportsFileOperations: true, // Files API now implemented
            supportsStructuredOutput: true,
            maxInputTokens: 200000, // 200K context window
            maxOutputTokens: 10000, // 10K output tokens for Claude 4
            costPerMillionTokens: {
              input: modelName.includes('opus') ? 15 : 3, // Opus is more expensive
              output: modelName.includes('opus') ? 75 : 15,
            },
          };
        }
        return {
          supportsFileOperations: true, // Files API now implemented
          supportsStructuredOutput: true, // Claude 3.5 supports structured output
          maxInputTokens: 200000, // 200K context window
          maxOutputTokens: 8192, // 8K output tokens
          costPerMillionTokens: {
            input: modelName.includes('haiku') ? 0.25 : 3,
            output: modelName.includes('haiku') ? 1.25 : 15,
          },
        };
      case 'gemini': {
        // Get model info from Gemini
        const modelInfo = this.getGeminiInstance().supportsModel(modelName)
          ? { maxInputTokens: 1048576, maxOutputTokens: 65536 }
          : { maxInputTokens: 128000, maxOutputTokens: 8192 };

        return {
          supportsFileOperations: true,
          supportsStructuredOutput: true,
          maxInputTokens: modelInfo.maxInputTokens,
          maxOutputTokens: modelInfo.maxOutputTokens,
          costPerMillionTokens: {
            input: 3.5,
            output: 10.5,
          },
        };
      }
      default:
        return {
          supportsFileOperations: false,
          supportsStructuredOutput: false,
          maxInputTokens: 32000,
          maxOutputTokens: 4096,
        };
    }
  }

  /**
   * Get list of supported models
   */
  getSupportedModels(): Array<{
    name: string;
    provider: string;
    capabilities: ModelCapabilities;
    baseSystemPrompt?: string;
    specialPrefixes?: Record<string, string>;
  }> {
    return Object.entries(UNIFIED_AI_MODELS).map(([name, config]) => ({
      name,
      provider: config.provider,
      capabilities: this.getModelCapabilities(name),
      baseSystemPrompt: this.getBaseSystemPrompt(name),
      specialPrefixes: this.getSpecialPrefixes(name),
    }));
  }

  /**
   * Get base system prompt for a model
   */
  getBaseSystemPrompt(modelName: string): string | undefined {
    const config = UNIFIED_AI_MODELS[modelName as UnifiedModelName];
    if (!config) {
      return undefined;
    }

    switch (config.provider) {
      case 'openai':
        return BASE_SYSTEM_PROMPTS.openai;
      case 'claude':
        return BASE_SYSTEM_PROMPTS.anthropic;
      case 'gemini':
        return BASE_SYSTEM_PROMPTS.google;
      default:
        return undefined;
    }
  }

  /**
   * Get special prefixes for a model (e.g., JSON prefill for Claude)
   */
  getSpecialPrefixes(modelName: string): Record<string, string> | undefined {
    const config = UNIFIED_AI_MODELS[modelName as UnifiedModelName];
    if (!config) {
      return undefined;
    }

    if (config.provider === 'claude') {
      return {
        jsonPrefill: PROMPT_PREFIXES.claude_json_prefill
      };
    }

    return undefined;
  }

  /**
   * Get models that support a specific capability
   */
  getModelsByCapability(capability: keyof ModelCapabilities, value: any = true): string[] {
    return this.getSupportedModels()
      .filter(model => model.capabilities[capability] === value)
      .map(model => model.name);
  }

  getDefaultModel(): string {
    return this.defaultModel;
  }
}

export const getUnifiedAIModelFactory = () => {
  if (!instance) {
    instance = new UnifiedAIModelFactory(wwgLogger);
  }
  return instance;
};
