/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import Automizer, { ChartData, CmToDxa, ISlide, modify, ModifyImageHelper, ModifyShapeHelper } from 'pptx-automizer';
import {
  CustomChartData,
  PPTXTemplateConfig,
  PPTXTemplateInterface,
  PPTXTemplateLayoutItem,
  PPTXTemplateTableReplacementRow,
} from './templates/PPTXTemplateInterface';
import { ShapeModificationCallback, SlideModificationCallback } from 'pptx-automizer/dist/types/types';
import ModifyPresentationHelper from 'pptx-automizer/dist/helper/modify-presentation-helper';
import { PPTXTemplateName, TemplateContext } from './types';
import { ModifyXmlCallback } from 'pptx-automizer/dist/types/xml-types';
import axios from 'axios';
import fs from 'fs/promises';
import { customDateFormat, DateFormat } from '../../util/date';
import { wwgLogger } from '../wwgLogger';
import { extractImageFromUrl } from './utils';

interface ImageReplacement {
  id: string;
  fileName?: string | null;
  replace?: {
      elementId: string;
      x: number | null;
      y: number | null;
  } | null;
}

export class PPTXTemplate implements PPTXTemplateInterface {
  /**
   * THEME_NAME is used as short name for template file instead of its location.
   * @type {string}
   * @private
   */
  private THEME_NAME: string = 'theme';
  private mediaLocation = '/tmp/tmp';
  private imageReplacements: Map<number, ImageReplacement[]> = new Map();

  constructor(
    private config: PPTXTemplateConfig,
    private presentation: Automizer,
    private context: TemplateContext
  ) {
    this.mediaLocation = `/tmp/${this.context.initiativeId}-${customDateFormat(new Date(), DateFormat.FileName)}`;
    this.presentation
      .loadRoot(this.getRootByTemplate(this.context.templateName))
      .load(this.config.templateFilename, this.THEME_NAME)
  }

  private async getImageUrlAsBuffer(url: string): Promise<Buffer | null> {
    const res = await axios.get<string>(url, { responseType: 'arraybuffer' });
    return Buffer.from(res.data, 'utf-8');
  }

  private async saveImageUrl(imageUrl: string) {
    const imageBuffer = await this.getImageUrlAsBuffer(imageUrl);
    if (!imageBuffer) {
      return null;
    }
    const fileName = extractImageFromUrl(imageUrl);
    const filePath = `${this.mediaLocation}/${fileName}`;
    await fs.writeFile(filePath, imageBuffer);
    return fileName ?? null;
  }

  private getRootByTemplate(templateName: PPTXTemplateName) {
    switch (templateName) {
      case PPTXTemplateName.MT:
        return 'MTRoot.pptx';
      case PPTXTemplateName.CT:
      default:
        return 'Root.pptx';
    }
  }

  private async cleanup() {
    await fs.rm(this.mediaLocation, { recursive: true });
  }

  private async saveImageReplacements() {
    await fs.mkdir(this.mediaLocation, { recursive: true });

    const fileNames: string[] = [];

    let slideNum = 0;
    for (const slide of this.config.slides) {
      slideNum++;
      if (!slide.imageReplacements) {
        continue;
      }
      const slideReplacements: ImageReplacement[] = [];

      for (const [id, replacementConfig] of slide.imageReplacements) {
        const imageUrl = await replacementConfig.imageUrl?.();
        if (!imageUrl) {
          // to add SDGs image replace
          slideReplacements.push({
            id,
            replace: await replacementConfig.replace?.(),
          });
          continue;
        }
        const fileName = await this.saveImageUrl(imageUrl);
        if (fileName) {
          fileNames.push(fileName);
          slideReplacements.push({
            id,
            fileName,
            replace: await replacementConfig.replace?.(),
          });
        }
      }
      this.imageReplacements.set(slideNum, slideReplacements);
    }

    return this.presentation.loadMedia(fileNames, this.mediaLocation);
  }

  private applyImageReplacements(slide: ISlide, imageReplacements: ImageReplacement[]) {
    for (const imageReplacement of imageReplacements) {
      if (imageReplacement.fileName) {
        slide.modifyElement(imageReplacement.id, [
          ModifyImageHelper.setRelationTarget(imageReplacement.fileName) as ShapeModificationCallback
        ]);
      }

      if (imageReplacement.replace) {
        const replace = imageReplacement.replace;
        if (replace.x && replace.y) {
          slide.modifyElement(replace.elementId, [
            ModifyShapeHelper.setPosition({
              x: CmToDxa(replace.x),
              y: CmToDxa(replace.y),
            }) as ShapeModificationCallback,
          ]);
        } else {
          slide.removeElement(replace.elementId);
        }
      }

      if (!imageReplacement.fileName && !imageReplacement.replace) {
        // There's nothing to do on this image, so we remove it
        slide.removeElement(imageReplacement.id);
      }
    }
  }

  private async getTextReplacements(
    replacements: PPTXTemplateLayoutItem['textReplacements'],
    slideNum: number,
    repeatNum: number
  ): Promise<{ replace: string; text: string }[]> {
    const outputs: { replace: string; text: string }[] = [];
    if (!replacements) {
      return outputs;
    }

    for (const [replace, replacementConfig] of replacements) {
      const text = await replacementConfig.text({ slideNum, repeatNum });
      outputs.push({
        replace,
        text: text ? String(text) : '-',
      });
    }
    return outputs;
  }

  private async applyTextReplacements(
    slide: Pick<ISlide, 'modify'>,
    replacements: PPTXTemplateLayoutItem['textReplacements'],
    slideNum: number,
    repeatNum: number = 1
  ) {
    const textReplacements = await this.getTextReplacements(replacements, slideNum, repeatNum);
    slide.modify(
      modify.replaceText(
        textReplacements.map(({ replace, text }) => ({
          replace,
          by: {
            text,
          },
        }))
      ) as unknown as SlideModificationCallback
    );
  }

  private async getChartReplacements(
    replacements: PPTXTemplateLayoutItem['chartReplacements'],
    slideNum: number,
    repeatNum: number
  ): Promise<{ id: string; chartData: CustomChartData }[]> {
    const outputs: { id: string; chartData: CustomChartData }[] = [];
    if (!replacements) {
      return outputs;
    }

    for (const [id, replacementConfig] of replacements) {
      const chartData = await replacementConfig.chartData({ slideNum, repeatNum });
      outputs.push({
        id,
        chartData,
      });
    }
    return outputs;
  }

  private async applyChartReplacements(
    slide: ISlide,
    replacements: PPTXTemplateLayoutItem['chartReplacements'],
    slideNum: number,
    repeatNum: number = 1
  ) {
    const chartReplacements = await this.getChartReplacements(replacements, slideNum, repeatNum);
    chartReplacements?.map((chartReplacement) => {
      slide.modifyElement(chartReplacement.id, [modify.setChartData(chartReplacement.chartData as ChartData)]);
    });
  }

  private async getTableReplacements(
    replacements: PPTXTemplateLayoutItem['tableReplacements'],
    slideNum: number,
    repeatNum: number
  ): Promise<{ id: string; tableData: PPTXTemplateTableReplacementRow[] }[]> {
    const outputs: { id: string; tableData: PPTXTemplateTableReplacementRow[] }[] = [];
    if (!replacements) {
      return outputs;
    }

    for (const [id, replacementConfig] of replacements) {
      const tableData = await replacementConfig.rows({ slideNum, repeatNum });
      if (tableData) {
        outputs.push({
          id,
          tableData,
        });
      }
    }
    return outputs;
  }

  private async applyTableReplacements(
    slide: ISlide,
    replacements: PPTXTemplateLayoutItem['tableReplacements'],
    slideNum: number,
    repeatNum: number = 1
  ) {
    const tableReplacements = await this.getTableReplacements(replacements, slideNum, repeatNum);
    tableReplacements?.map((tableReplacement) => {
      const tableData = {
        body: tableReplacement.tableData,
      };
      slide.modifyElement(tableReplacement.id, [
        // Table data can also support undefined if needed
        modify.setTable(tableData, { adjustWidth: false, adjustHeight: false, expand: [
          // This is unrelevant but without it the data populated for table with merged cells will be incorrect
          // Seems like an issue with the package, may not work in the future
          {
            mode: 'column',
            tag: '{{each:col1}}',
            count: 1,
          },
        ] }),
      ]);
    });
  }

  public async generate() {
    await this.saveImageReplacements();

    // Add Master Slides
    if (this.config.masterSlides) {
      for (const layoutItem of this.config.masterSlides) {
        await new Promise<void>((resolve) => {
          // layoutItem.slideId here is the index of the slide we want to add from the template
          // THEME_NAME is used as short name for the current template
          this.presentation.addMaster(this.THEME_NAME, layoutItem.slideId, async (master) => {
            await this.applyTextReplacements(
              master,
              [...(this.config.textReplacements ?? []), ...(layoutItem.textReplacements ?? [])],
              0
            );
            resolve();
          });
        });
      }
    }
    // End Add Master Slides

    let slideNum = 0;
    // Track the number of slides generated, including repeat slides
    let slideCount = 0;
    // Add Slides
    for (const layoutItem of this.config.slides) {
      const skip = layoutItem.skip ? await layoutItem.skip() : false;

      if (skip) {
        continue;
      }

      slideNum++;
      slideCount++;

      const slideRepeat = layoutItem.slideRepeat ? await layoutItem.slideRepeat() : 1;
      slideCount += slideRepeat - 1;
      for (let repeatNum = 1; repeatNum <= slideRepeat; repeatNum++) {
        await new Promise<void>((resolve) => {
          this.presentation.addSlide(this.THEME_NAME, layoutItem.slideId, async (slide) => {
            const utrCodes = await layoutItem.appendix?.({ slideNum });
            if (utrCodes) {
              await this.config.appendixCallback?.({ utrCodes, slideNum });
            }

            const tocEntries = layoutItem.getTOCEntries?.({ slideNum });
            if (tocEntries?.length) {
              await this.config.tableOfContentsCallback?.({ entries: tocEntries });
            }

            await this.applyTextReplacements(
              slide,
              [...(this.config.textReplacements ?? []), ...(layoutItem.textReplacements ?? [])],
              slideNum,
              repeatNum
            );

            await this.applyChartReplacements(slide, layoutItem.chartReplacements, slideNum, repeatNum);

            await this.applyTableReplacements(slide, layoutItem.tableReplacements, slideNum, repeatNum);

            this.applyImageReplacements(slide, this.imageReplacements.get(slideNum) ?? []);

            resolve();
          });
        });
      }
    }
    // End Add Slides

    // Sort slides if needed
    if (this.config.getCustomSlidesOrder) {
      const customSlidesOrder = this.config.getCustomSlidesOrder(slideCount);
      if (customSlidesOrder) {
        this.presentation.modify(ModifyPresentationHelper.sortSlides(customSlidesOrder) as ModifyXmlCallback);
      }
    }

    console.log('Completed replacing text placeholders.');

    const zipFile = await this.presentation.getJSZip();

    try {
      await this.cleanup();
    }
    catch (e) {
      wwgLogger.error(`Error running cleanup. Continuing anyway, but some files might be left in ${this.mediaLocation} folder`);
    }

    console.log('Generated ZIP file.');

    return zipFile;
  }
}
