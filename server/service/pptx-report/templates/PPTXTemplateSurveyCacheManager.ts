/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { PPTXTemplateSurveyCache } from './PPTXTemplateSurveyCache';

export class PPTXTemplateSurveyCacheManager {
  private repositories: Map<string, PPTXTemplateSurveyCache> = new Map();

  constructor(private surveyId: ObjectId, private initiativeId: ObjectId) {}

  getCachedSurvey(offset: number = 0) {
    const key = `${this.surveyId.toHexString()}-${this.initiativeId.toHexString()}-${offset}`;
    if (!this.repositories.has(key)) {
      this.repositories.set(key, new PPTXTemplateSurveyCache(this.surveyId, this.initiativeId, offset));
    }
    return this.repositories.get(key);
  }
}
