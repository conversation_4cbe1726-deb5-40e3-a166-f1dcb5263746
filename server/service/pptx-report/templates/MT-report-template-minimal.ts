/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { DoubleMaterialityTemplateContext, FinancialPPTXTemplateContext, MTPPTXTemplateScheme } from '../types';
import { PPTXTemplateConfig } from './PPTXTemplateInterface';
import { FinancialBuilder } from './builders/materiality-tracker/FinancialBuilder';
import { MaterialityAssessmentService } from '../../../service/materiality-assessment/MaterialityAssessmentService';
import { ObjectId } from 'bson';
import { customDateFormat, DateFormat } from '../../../util/date';
import { pptxMTConfigAppendix } from './MT-report-template-minimal/MT-report-template-minimal-appendix';
import {
  pptxMTConfigConclusion,
  pptxMTConfigContents,
  pptxMTConfigCover,
  pptxMTConfigPillarsBoundariesSummary,
} from './MT-report-template-minimal/MT-report-template-minimal-static-slides';
import { pptxMTConfigPillars } from './MT-report-template-minimal/MT-report-template-minimal-pillars';
import { pptxDoubleMaterialityRelevantTopics, pptxMTConfigRelevantTopics } from './MT-report-template-minimal/MT-report-template-minimal-relevance';
import { pptxDoubleMaterialityBoundaries, pptxMTConfigBoundaries } from './MT-report-template-minimal/MT-report-template-minimal-boundaries';
import { getMaterialityAssessmentManager } from '../../materiality-assessment/MaterialityAssessmentManager';
import { MaterialTopicRepository } from '../../materiality-assessment/MaterialTopicRepository';
import { DoubleMaterialityBuilder } from './builders/materiality-tracker/DoubleMaterialityBuilder';
import { pptxMTConfigIntro } from './MT-report-template-minimal/MT-report-template-minimal-intro';
import { MaterialityAssessmentType } from '../../../models/materialityMetric';

const getTemplateFile = (scheme: string | MTPPTXTemplateScheme): string => {
  switch (scheme) {
    case MTPPTXTemplateScheme.DoubleMateriality:
      return 'MT-double-materiality-report-template-minimal-scheme-horizontal.pptx';
    case MTPPTXTemplateScheme.FinancialReport:
    default:
      return 'MT-report-template-minimal-scheme-horizontal.pptx';
  }
};

const createFinancialBuilder = async ({
  initiativeId,
  financialAssessmentDataMin,
  surveyId,
}: FinancialPPTXTemplateContext) => {
  const MAService = new MaterialityAssessmentService(new ObjectId(surveyId));
  const sizeScope = await getMaterialityAssessmentManager().getSizeScopeByAssessmentId(surveyId);
  const maxScore = await MaterialTopicRepository.findMaxScore(sizeScope, MaterialityAssessmentType.Financial);
  const financialAssessmentData = await MAService.hydrateFinancialTopicData(financialAssessmentDataMin);
  return new FinancialBuilder(initiativeId, financialAssessmentData, surveyId, sizeScope, maxScore);
};

export const getMTFinancialConfig = async (
  context: FinancialPPTXTemplateContext,
  externalBuilder?: FinancialBuilder
): Promise<PPTXTemplateConfig> => {
  const {
    templateScheme = MTPPTXTemplateScheme.FinancialReport,
    metadata: { effectiveDate },
  } = context;

  const builder = externalBuilder ?? (await createFinancialBuilder(context));

  return {
    templateFilename: getTemplateFile(templateScheme),
    textReplacements: [
      ['COMPANY_NAME', { text: async () => await builder.getCompanyName() }],
      ['DATE', { text: async () => customDateFormat(effectiveDate, DateFormat.MonthYear).toUpperCase() }],
    ],
    masterSlides: [
      {
        slideId: 1, // Based on the slide template
      },
    ],
    tableOfContentsCallback: async ({ entries }) => {
      entries.forEach((entry) => {
        builder.addTOCEntry(entry);
      });
    },
    slides: [
      ...pptxMTConfigCover,
      ...pptxMTConfigContents,
      ...pptxMTConfigIntro(effectiveDate),
      ...pptxMTConfigRelevantTopics(builder),
      ...pptxMTConfigPillarsBoundariesSummary,
      ...(await pptxMTConfigBoundaries(builder)),
      ...(await pptxMTConfigPillars(builder)),
      ...pptxMTConfigConclusion,
      ...(await pptxMTConfigAppendix(builder)),
    ],
  };
};

const createDoubleMaterialityBuilder = async ({
  initiativeId,
  assessmentDataMin,
  surveyId,
  metadata: { hasCustomOrder },
}: DoubleMaterialityTemplateContext) => {
  const MAService = new MaterialityAssessmentService(new ObjectId(surveyId));
  const assessmentResult = await MAService.hydrateTopics(assessmentDataMin);
  const sizeScope = await getMaterialityAssessmentManager().getSizeScopeByAssessmentId(surveyId);
  return new DoubleMaterialityBuilder(initiativeId, assessmentResult, surveyId, sizeScope, hasCustomOrder);
};

export const getMTDoubleMaterialityConfig = async (
  context: DoubleMaterialityTemplateContext,
  externalBuilder?: DoubleMaterialityBuilder
): Promise<PPTXTemplateConfig> => {
  const {
    templateScheme,
    metadata: { effectiveDate },
  } = context;

  const builder = externalBuilder ?? await createDoubleMaterialityBuilder(context);

  return {
    templateFilename: getTemplateFile(templateScheme),
    textReplacements: [
      ['COMPANY_NAME', { text: async () => await builder.getCompanyName() }],
      ['DATE', { text: async () => customDateFormat(effectiveDate, DateFormat.MonthYear).toUpperCase() }],
    ],
    masterSlides: [
      {
        slideId: 1, // Based on the slide template
      },
    ],
    tableOfContentsCallback: async ({ entries }) => {
      entries.forEach((entry) => {
        builder.addTOCEntry(entry);
      });
    },
    slides: [
      ...pptxMTConfigCover,
      ...pptxMTConfigContents,
      ...pptxMTConfigIntro(effectiveDate),
      ...pptxDoubleMaterialityRelevantTopics(builder),
      ...pptxMTConfigPillarsBoundariesSummary,
      ...(await pptxDoubleMaterialityBoundaries(builder)),
      ...(await pptxMTConfigPillars(builder)),
      ...pptxMTConfigConclusion,
      ...(await pptxMTConfigAppendix(builder)),
    ],
  };
};
