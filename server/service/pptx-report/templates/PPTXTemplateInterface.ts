/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ChartData } from 'pptx-automizer';
import { ChartBubble, ChartCategory, ChartPoint, ChartPointValue } from 'pptx-automizer/dist/types/chart-types';
import JSZip from 'jszip';

export type CustomChartPointValue = ChartPointValue | string;

export type CustomChartCategory = Omit<ChartCategory, 'values'> & {
  values: (CustomChartPointValue | ChartPoint | ChartBubble)[];
};

export type CustomChartData = Omit<ChartData, 'categories'> & {
  categories: CustomChartCategory[];
};
interface PPTXTemplateChartReplacement {
  chartData: (context: { slideNum: number, repeatNum?: number }) => Promise<CustomChartData>;
}

export interface TOCEntry {
  /**
   * Unique to identify the entry.
   */
  key: string;
  /**
   * Title to display.
   */
  title: string;
  /**
   * Indent of the title. The higher the number, the more indented the title will be.
   */
  indentLevel?: number;

  /**
   * Page number in the table of contents.
   */
  pageNumber: number;
}

// PPTX table types
export type TableData = string | number;

export interface TableDataRow {
  values: TableData[];
}

export interface PPTXTemplateImageReplacement {
  imageUrl?: () => Promise<string | null>;
  replace?: () => Promise<{
    elementId: string;
    x: number | null; // cms
    y: number | null; // cms
  } | null>;
}

export type PPTXTemplateTableReplacementRow = {
  values: (string | number)[];
};

interface PPTXTemplateTableReplacement {
  rows: (context: { slideNum: number, repeatNum?: number }) => Promise<PPTXTemplateTableReplacementRow[] | null>;
}

export interface PPTXTemplateReplacementText {
  text: (context: { slideNum: number, repeatNum?: number }) => Promise<string | number | null | undefined>;
}

export type PPTXTemplateReplacement<T = PPTXTemplateReplacementText> = [string, T];

export interface PPTXTemplateLayoutItem {
  slideId: number;
  skip?: () => Promise<boolean>;
  appendix?: (context: { slideNum: number }) => Promise<string[]>;
  getTOCEntries?: (context: { slideNum: number }) => TOCEntry[];
  slideRepeat?: () => Promise<number>;
  textReplacements?: PPTXTemplateReplacement[];
  chartReplacements?: PPTXTemplateReplacement<PPTXTemplateChartReplacement>[];
  imageReplacements?: PPTXTemplateReplacement<PPTXTemplateImageReplacement>[];
  tableReplacements?: PPTXTemplateReplacement<PPTXTemplateTableReplacement>[];
}

export interface PPTXTemplateConfig {
  templateFilename: string;
  textReplacements?: PPTXTemplateReplacement[];
  appendixCallback?: (context: { utrCodes: string[], slideNum: number }) => Promise<void>;
  tableOfContentsCallback?: (context: { entries: TOCEntry[] }) => Promise<void>;
  slides: PPTXTemplateLayoutItem[];
  masterSlides?: PPTXTemplateLayoutItem[];
  getCustomSlidesOrder?: (lastSlideNum: number) => number[] | undefined;
}

export interface PPTXTemplateInterface {
  generate(): Promise<JSZip>;
}

// types for input data Map

export interface InputData {
  value: number | string | undefined;
  unit?: string;
  numberScale?: string;
}

export interface ValueListInputData extends InputData {
  valueListCode: string;
}

export interface TableInputData extends InputData {
  code: string;
}

export type InputMapData = InputData | TableInputData[][] | ValueListInputData[];

export interface SlideData {
  utrsInputMap: Map<string, InputMapData>;
  unit?: string;
  numberScale?: string;
}

export const isInputData = (inputs: InputMapData): inputs is InputData => {
  return !Array.isArray(inputs) && 'value' in inputs;
};

export const isTableInputData = (inputs: InputMapData): inputs is TableInputData[][] => {
  if (!Array.isArray(inputs)) {
    return false;
  }
  return inputs.length === 0 || Array.isArray(inputs[0]);
};

export const isValueListInputData = (inputs: InputMapData): inputs is ValueListInputData[] => {
  if (!Array.isArray(inputs)) {
    return false;
  }
  return inputs.length === 0 || 'valueListCode' in inputs[0];
};
