/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { DECIMAL_PLACES } from '../../constants';
import { BaseBuilder } from '../builders/materiality-tracker/BaseBuilder';
import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';

const MAX_ROWS = 5;

export const pptxMTConfigAppendix = async (builder: BaseBuilder): Promise<PPTXTemplateLayoutItem[]> => [
  {
    slideId: 10,
    slideRepeat: async () => Math.ceil(builder.getTopicsLength() / MAX_ROWS),
    textReplacements: [
      [
        'APPENDIX',
        {
          text: async ({ repeatNum = 1 }) => `APPENDIX ${repeatNum}`,
        },
      ],
    ],
    tableReplacements: [
      [
        'APPENDIX_TOPICS',
        {
          rows: async ({ repeatNum = 1 }) => {
            const offset = (repeatNum - 1) * MAX_ROWS;
            const table = await (await builder.getTableBuilder())
              .maxDecimals(DECIMAL_PLACES)
              .getDefinitionsTable({ startOffset: offset, endOffset: offset + MAX_ROWS });
            if (!table) {
              return null;
            }
            return [{ values: ['Material topic', 'Definition'] }, ...table];
          },
        },
      ],
    ],
  },
];
