/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';
import { displayValueWithSuffix } from './utils';

export const getPPTXConfigEnvironmental = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Environmental
    slideId: 14,
  },
  {
    // Emissions total 1
    slideId: 15,
    appendix: async () => ['sgx-core-1a', 'sgx-custom-56', 'gri/2020/305-1/a', 'gri/2020/305-2/a', 'gri/2020/305-3/a'],
    textReplacements: [
      [
        'COLUMN_1_TITLE',
        {
          text: async () => {
            const scope3 = await builder.createUTRBuilder('gri/2020/305-3/a').getAsNumber();
            if (scope3 === null) {
              return 'Scope 1 & 2 greenhouse gas emissions';
            }
            return 'Scope 1, 2 & 3 greenhouse gas emissions';
          },
        },
      ],
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const totalEmissionsAnswer = await builder.createUTRBuilder('sgx-core-1a').getInputSimpleNumericAnswer();
            // @TODO - this seems wrong as there are multiple intensities and multiple rows on this table... is there a better way to get this?
            const totalEmissionsIntensity = await builder
              .createUTRTableBuilder('sgx-custom-56')
              .getInputNumericAnswer('sgx56-emissions-intens-rev');
            const scope1Answer = await builder.createUTRBuilder('gri/2020/305-1/a').getInputSimpleNumericAnswer();
            const scope2Answer = await builder.createUTRBuilder('gri/2020/305-2/a').getInputSimpleNumericAnswer();
            const scope3Answer = await builder.createUTRBuilder('gri/2020/305-3/a').getInputSimpleNumericAnswer();

            let breakdown = '';
            if (scope3Answer.value === null) {
              breakdown = `Scope 1 has ${displayValueWithSuffix(scope1Answer)} and Scope 2 has ${displayValueWithSuffix(
                scope1Answer
              )}`;
            } else {
              breakdown = `Scope 1 has ${displayValueWithSuffix(scope1Answer)}, Scope 2 has ${displayValueWithSuffix(
                scope2Answer
              )} and Scope 3 has ${displayValueWithSuffix(scope3Answer)}`;
            }

            return builder
              .createAIBuilder()
              .ask([
                `As a company I have total greenhouse gas emissions of ${displayValueWithSuffix(
                  totalEmissionsAnswer
                )}, ` + `which is broken down into ${breakdown}.`,
              ])
              .addTarget('sgx-core-1a')
              .and(`My total revenue emissions intensity is ${displayValueWithSuffix(totalEmissionsIntensity)}.`)
              .max(150)
              .exec();
          },
        },
      ],
    ],
    chartReplacements: [
      [
        'DONUT_CHART',
        {
          chartData: async () => {
            const categories = [
              {
                label: 'Scope 1',
                values: [await builder.createUTRBuilder('gri/2020/305-1/a').fallback(0.01).getAsNumber()],
              },
              {
                label: 'Scope 2',
                values: [await builder.createUTRBuilder('gri/2020/305-2/a').fallback(0.01).getAsNumber()],
              },
            ];
            const scope3 = await builder.createUTRBuilder('gri/2020/305-3/a').getAsNumber();
            if (scope3 !== null) {
              categories.push({
                label: 'Scope 3',
                values: [scope3],
              });
            }

            return {
              series: [
                {
                  label: String(await builder.getYear()),
                },
              ],
              categories,
            };
          },
        },
      ],
    ],
  },
  {
    // Emissions total 2
    slideId: 16,
    appendix: async () => [
      'sgx-custom-57',
      'sgx-custom-58',
      'gri/2020/305-1/a',
      'gri/2020/305-2/a',
      'gri/2020/305-2/b',
    ],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            // @TODO - this seems wrong as there are multiple intensities and multiple rows on this table... is there a better way to get this?
            const scope1EmissionsIntensity = await builder
              .createUTRTableBuilder('sgx-custom-57')
              .get('sgx57-emissions-intens-rev');
            const scope2EmissionsIntensity = await builder
              .createUTRTableBuilder('sgx-custom-58')
              .get('sgx-58-emissions-intense-revenue');

            const scope1 = await builder.createUTRBuilder('gri/2020/305-1/a').getAsNumber();
            const locationScope2 = await builder.createUTRBuilder('gri/2020/305-2/a').getAsNumber();
            const marketBasedScope2 = await builder.createUTRBuilder('gri/2020/305-2/b').getAsNumber();
            return builder
              .createAIBuilder()
              .ask([
                `As a company, my emissions breakdown is:`,
                `Scope 1 emissions: ${scope1 ? `${scope1}mt2` : 'Not Reported'}`,
                `Scope 2 location-based emissions: ${locationScope2 ? `${locationScope2}mt2` : 'Not Reported'}`,
                `Scope 2 market-based emissions: ${marketBasedScope2 ? `${marketBasedScope2}mt2` : 'Not Reported'}`,
                '',
                `My emissions intensity breakdown is:`,
                `Scope 1 emissions intensity: ${
                  scope1EmissionsIntensity ? `${scope1EmissionsIntensity}mt2` : 'Not Reported'
                }`,
                `Scope 2 emissions intensity: ${
                  scope2EmissionsIntensity ? `${scope2EmissionsIntensity}mt2` : 'Not Reported'
                }`,
              ])
              .addTarget('sgx-custom-57')
              .addTarget('sgx-custom-58')
              .addFurtherExplanation('sgx-custom-57')
              .addFurtherExplanation('sgx-custom-58')
              .narrative()
              .firstPerson()
              .and(
                `Use references to the data supplied, explaining the figures and what the figures mean - ` +
                  'expanding on what location-based mean in this context if you have data for that'
              )
              .max(120)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_SCOPE_1_2',
        {
          text: async () => {
            const scope1 = await builder.createUTRBuilder('gri/2020/305-1/a').periodOffset(-1).getAsNumber();
            const scope2 = await builder.createUTRBuilder('gri/2020/305-2/a').periodOffset(-1).getAsNumber();
            const sum = (scope1 ?? 0) + (scope2 ?? 0);
            builder.setKeyStore('PREV_YEAR_SCOPE_1_2', sum);
            return sum;
          },
        },
      ],
      [
        'YEAR_SCOPE_1_2',
        {
          text: async () => {
            const scope1 = await builder.createUTRBuilder('gri/2020/305-1/a').getAsNumber();
            const scope2 = await builder.createUTRBuilder('gri/2020/305-2/a').getAsNumber();
            const sum = (scope1 ?? 0) + (scope2 ?? 0);
            builder.setKeyStore('YEAR_SCOPE_1_2', sum);
            return sum;
          },
        },
      ],
      [
        'CHANGE_SCOPE_1_2',
        {
          text: async () => {
            const valueA = builder.getKeyStore('PREV_YEAR_SCOPE_1_2');
            const valueB = builder.getKeyStore('YEAR_SCOPE_1_2');
            return builder.getChangeText(Number(valueA), Number(valueB));
          },
        },
      ],
      [
        'PREV_YEAR_EMISSIONS_INT_SCOPE_1',
        {
          text: async () => {
            const intensity = await builder
              .createUTRTableBuilder('sgx-custom-57')
              .periodOffset(-1)
              .maxDecimals(2)
              .getAsNumber('sgx57-emissions-intens-rev');
            builder.setKeyStore('PREV_YEAR_EMISSIONS_INT_SCOPE_1', intensity);
            return intensity;
          },
        },
      ],
      [
        'YEAR_EMISSIONS_INT_SCOPE_1',
        {
          text: async () => {
            const intensity = await builder
              .createUTRTableBuilder('sgx-custom-57')
              .maxDecimals(2)
              .getAsNumber('sgx57-emissions-intens-rev');
            builder.setKeyStore('YEAR_EMISSIONS_INT_SCOPE_1', intensity);
            return intensity;
          },
        },
      ],
      [
        'CHANGE_EMISSIONS_INT_SCOPE_1',
        {
          text: async () => {
            const valueA = builder.getKeyStore('PREV_YEAR_EMISSIONS_INT_SCOPE_1');
            const valueB = builder.getKeyStore('YEAR_EMISSIONS_INT_SCOPE_1');
            return builder.getChangeText(Number(valueA), Number(valueB));
          },
        },
      ],
      // [
      //   'PREV_YEAR_EMISSIONS_INT_SCOPE_2',
      //   {
      //     text: async () => {
      //       const intensity = await builder
      //         .createUTRTableBuilder('sgx-custom-58')
      //         .periodOffset(-1)
      //         .getAsString('sgx-58-emissions-intense-revenue');
      //       builder.setKeyStore('PREV_YEAR_EMISSIONS_INT_SCOPE_2', intensity);
      //       return intensity;
      //     },
      //   },
      // ],
      // [
      //   'YEAR_EMISSIONS_INT_SCOPE_2',
      //   {
      //     text: async () => {
      //       const intensity = await builder
      //         .createUTRTableBuilder('sgx-custom-58')
      //         .getAsString('sgx-58-emissions-intense-revenue');
      //       builder.setKeyStore('YEAR_EMISSIONS_INT_SCOPE_2', intensity);
      //       return intensity;
      //     },
      //   },
      // ],
      // [
      //   'CHANGE_EMISSIONS_INT_SCOPE_2',
      //   {
      //     text: async () => {
      //       const valueA = builder.getKeyStore('PREV_YEAR_EMISSIONS_INT_SCOPE_2');
      //       const valueB = builder.getKeyStore('YEAR_EMISSIONS_INT_SCOPE_2');
      //       return builder.getChangeText(Number(valueA), Number(valueB));
      //     },
      //   },
      // ],
    ],
    chartReplacements: [
      [
        'DONUT_CHART_1',
        {
          chartData: async () => ({
            series: [
              {
                label: String(await builder.getYear()),
              },
            ],
            categories: [
              {
                label: 'Scope 1',
                values: [await builder.createUTRBuilder('gri/2020/305-1/a').fallback(0).getAsNumber()],
              },
              {
                label: 'Scope 2',
                values: [await builder.createUTRBuilder('gri/2020/305-2/a').fallback(0).getAsNumber()],
              },
            ],
          }),
        },
      ],
      [
        'DONUT_CHART_2',
        {
          chartData: async () => ({
            series: [
              {
                label: String(await builder.getYear()),
              },
            ],
            categories: [
              {
                label: 'Scope 1',
                values: [await builder.createUTRBuilder('gri/2020/305-1/a').fallback(0).getAsNumber()],
              },
              {
                label: 'Scope 2',
                values: [await builder.createUTRBuilder('gri/2020/305-2/a').fallback(0).getAsNumber()],
              },
            ],
          }),
        },
      ],
    ],
  },
  {
    // Emissions total 3
    slideId: 17,
    skip: async () => {
      const hasCurrentYear = await builder.createUTRBuilder('gri/2020/305-3/a').hasValue();
      if (hasCurrentYear) {
        return false;
      }
      return !(await builder.createUTRBuilder('gri/2020/305-3/a').periodOffset(-1).hasValue());
    },
    appendix: async () => ['gri/2020/305-3/a', 'sgx-custom-60'],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const scope3 = await builder.createUTRBuilder('gri/2020/305-3/a').getAsNumber();
            const scope3Intensity = await builder
              .createUTRTableBuilder('sgx-custom-60')
              .get('sgx60-emissions-intense-revenue');
            return builder
              .createAIBuilder()
              .ask([
                scope3 && scope3Intensity
                  ? `As a company my Scope 3 emissions are ${scope3} and my Scope 3 emissions intensity is ${scope3Intensity}.`
                  : `As a company I have not reported my Scope 3 emissions.`,
              ])
              .addTarget('gri/2020/305-3/a')
              .addTarget('sgx-custom-60', 'sgx60-emissions-intense-revenue')
              .addFurtherExplanation('gri/2020/305-3/a')
              .addFurtherExplanation('sgx-custom-60')
              .narrative()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and(
                'There is no need to explain if you don’t have any corresponding data, just note that there was no data available.'
              )
              .max(150)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_SCOPE_3',
        { text: async () => builder.createUTRBuilder('gri/2020/305-3/a').periodOffset(-1).getAsNumber() },
      ],
      ['YEAR_SCOPE_3', { text: async () => builder.createUTRBuilder('gri/2020/305-3/a').getAsNumber() }],
      [
        'CHANGE_SCOPE_3',
        {
          text: async () => builder.getUTRValueChange('gri/2020/305-3/a', { reportOffsetFrom: 0, reportOffsetTo: -1 }),
        },
      ],
      [
        'PREV_YEAR_EMISSIONS_INT',
        {
          text: async () =>
            builder
              .createUTRTableBuilder('sgx-custom-60')
              .periodOffset(-1)
              .maxDecimals(2)
              .getAsNumber('sgx60-emissions-intense-revenue'),
        },
      ],
      [
        'YEAR_EMISSIONS_INT',
        {
          text: async () =>
            builder.createUTRTableBuilder('sgx-custom-60').maxDecimals(2).getAsNumber('sgx60-emissions-intense-revenue'),
        },
      ],
      [
        'CHANGE_EMISSIONS_INT',
        {
          text: async () => {
            const valueA = await builder
              .createUTRTableBuilder('sgx-custom-60')
              .periodOffset(-1)
              .get('sgx60-emissions-intense-revenue');
            const valueB = await builder.createUTRTableBuilder('sgx-custom-60').get('sgx60-emissions-intense-revenue');
            return builder.getChangeText(Number(valueA), Number(valueB));
          },
        },
      ],
    ],
  },
  {
    // Consumption - Energy
    slideId: 18,
    appendix: async () => ['gri/2020/302-1/e', 'gri/2020/302-3/a'],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const energy = await builder.createUTRBuilder('gri/2020/302-1/e').getAsNumber();
            const energyIntensity = await builder.createUTRBuilder('gri/2020/302-3/a').getAsNumber();
            return builder
              .createAIBuilder()
              .ask([
                energy && energyIntensity
                  ? `As a company I have a total energy consumption of ${energy} and an energy consumption intensity of ${energyIntensity}.`
                  : `As a company I have not reported my total energy consumption.`,
              ])
              .addTarget('gri/2020/302-1/e')
              .addTarget('gri/2020/302-3/a')
              .addFurtherExplanation('gri/2020/302-1/e')
              .addFurtherExplanation('gri/2020/302-3/a')
              .narrative()
              .max(150)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_TOTAL_ENERGY',
        {
          text: async () =>
            builder.createUTRBuilder('gri/2020/302-1/e').periodOffset(-1).maxDecimals(2).getAsNumber(),
        },
      ],
      [
        'YEAR_TOTAL_ENERGY',
        { text: async () => builder.createUTRBuilder('gri/2020/302-1/e').maxDecimals(2).getAsNumber() },
      ],
      ['CHANGE_TOTAL_ENERGY', { text: async () => builder.getUTRValueChange('gri/2020/302-1/e') }],
      [
        'PREV_YEAR_ENERGY_INT',
        {
          text: async () =>
            builder.createUTRBuilder('gri/2020/302-3/a').periodOffset(-1).maxDecimals(2).getAsNumber(),
        },
      ],
      [
        'YEAR_ENERGY_INT',
        { text: async () => builder.createUTRBuilder('gri/2020/302-3/a').maxDecimals(2).getAsNumber() },
      ],
      ['CHANGE_ENERGY_INT', { text: async () => builder.getUTRValueChange('gri/2020/302-3/a') }],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => {
            const year = await builder.getYear();
            const prevYear = await builder.getYear(-1);
            const prev = await builder
              .createUTRBuilder('gri/2020/302-1/e')
              .periodOffset(-1)
              .maxDecimals(2)
              .fallback(0)
              .getAsNumber();
            const current = await builder.createUTRBuilder('gri/2020/302-1/e').maxDecimals(2).fallback(0).getAsNumber();
            return {
              series: [
                {
                  label: 'Total energy consumption',
                },
              ],
              categories: [
                {
                  label: String(prevYear),
                  values: [prev],
                },
                {
                  label: String(year),
                  values: [current],
                },
              ],
            };
          },
        },
      ],
    ],
  },
  {
    // Consumption - Water
    slideId: 19,
    appendix: async () => ['gri/2020/303-5/a', 'survey/generic/water-intensity'],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const water = await builder.createUTRBuilder('gri/2020/303-5/a').getAsNumber();
            const waterIntesity = await builder.createUTRBuilder('survey/generic/water-intensity').getAsNumber();
            return builder
              .createAIBuilder()
              .ask([
                water && waterIntesity
                  ? `As a company I have a total water consumption of ${water} and a water consumption intensity of ${waterIntesity}.`
                  : `As a company I have not reported my total water consumption.`,
              ])
              .addTarget('gri/2020/303-5/a')
              .addTarget('survey/generic/water-intensity')
              .addFurtherExplanation('gri/2020/303-5/a')
              .addFurtherExplanation('survey/generic/water-intensity')
              .narrative()
              .max(150)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_TOTAL_WATER',
        { text: async () => builder.createUTRBuilder('gri/2020/303-5/a').periodOffset(-1).getAsNumber() },
      ],
      ['YEAR_TOTAL_WATER', { text: async () => builder.createUTRBuilder('gri/2020/303-5/a').getAsNumber() }],
      ['CHANGE_TOTAL_WATER', { text: async () => builder.getUTRValueChange('gri/2020/303-5/a') }],
      [
        'PREV_YEAR_WATER_INT',
        {
          text: async () =>
            builder.createUTRBuilder('survey/generic/water-intensity').periodOffset(-1).maxDecimals(2).getAsNumber(),
        },
      ],
      [
        'YEAR_WATER_INT',
        {
          text: async () =>
            builder.createUTRBuilder('survey/generic/water-intensity').maxDecimals(2).getAsNumber(),
        },
      ],
      ['CHANGE_WATER_INT', { text: async () => builder.getUTRValueChange('survey/generic/water-intensity') }],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => ({
            series: [
              {
                label: 'Total water consumption',
              },
            ],
            categories: [
              {
                label: String(await builder.getYear(-1)),
                values: [
                  await builder
                    .createUTRBuilder('gri/2020/303-5/a')
                    .periodOffset(-1)
                    .fallback(0)
                    .maxDecimals(2)
                    .getAsNumber(),
                ],
              },
              {
                label: String(await builder.getYear()),
                values: [await builder.createUTRBuilder('gri/2020/303-5/a').fallback(0).maxDecimals(2).getAsNumber()],
              },
            ],
          }),
        },
      ],
    ],
  },
  {
    // Consumption - Waste
    slideId: 20,
    appendix: async () => ['gri/2020/306-3/a'],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const hazardous = await builder.createUTRTableBuilder('gri/2020/306-3/a').sum(['hazardous_waste']);
            const nonHazardous = await builder.createUTRTableBuilder('gri/2020/306-3/a').sum(['non_hazardous']);
            const totalWaste = Number(hazardous ?? 0) + Number(nonHazardous ?? 0);
            builder.setKeyStore('YEAR_WASTE_HAZ', hazardous);
            builder.setKeyStore('YEAR_WASTE_NON_HAZ', nonHazardous);
            builder.setKeyStore('YEAR_TOTAL_WASTE', totalWaste);

            const prevHazardous = await builder
              .createUTRTableBuilder('gri/2020/306-3/a')
              .periodOffset(-1)
              .sum(['hazardous_waste']);
            const prevNonHazardous = await builder
              .createUTRTableBuilder('gri/2020/306-3/a')
              .periodOffset(-1)
              .sum(['non_hazardous']);
            const prevTotalWaste = Number(prevHazardous ?? 0) + Number(prevNonHazardous ?? 0);
            builder.setKeyStore('PREV_YEAR_WASTE_HAZ', prevHazardous);
            builder.setKeyStore('PREV_YEAR_WASTE_NON_HAZ', prevNonHazardous);
            builder.setKeyStore('PREV_YEAR_TOTAL_WASTE', prevTotalWaste);

            return builder
              .createAIBuilder()
              .ask(
                totalWaste
                  ? [
                      `As a company I have a total waste generation of ${totalWaste} which is broken down into the following categories:`,
                      `Hazardous (${hazardous ?? 0}mt) and Non-hazardous (${nonHazardous ?? 0}mt).`,
                    ]
                  : 'As a company I have not reported my total waste generation.'
              )
              .addTarget('gri/2020/306-3/a', 'hazardous_waste')
              .addTarget('gri/2020/306-3/a', 'non_hazardous')
              .addFurtherExplanation('gri/2020/306-3/a')
              .addFurtherExplanation('gri/2020/306-3/a')
              .narrative()
              .max(150)
              .exec();
          },
        },
      ],
      ['PREV_YEAR_TOTAL_WASTE', { text: async () => builder.getKeyStore('PREV_YEAR_TOTAL_WASTE') ?? '-' }],
      ['YEAR_TOTAL_WASTE', { text: async () => builder.getKeyStore('YEAR_TOTAL_WASTE') ?? '-' }],
      [
        'CHANGE_TOTAL_WASTE',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_TOTAL_WASTE')),
              Number(builder.getKeyStore('YEAR_TOTAL_WASTE'))
            ),
        },
      ],
      ['PREV_YEAR_WASTE_HAZ', { text: async () => builder.getKeyStore('PREV_YEAR_WASTE_HAZ') ?? '-' }],
      ['YEAR_WASTE_HAZ', { text: async () => builder.getKeyStore('YEAR_WASTE_HAZ') ?? '-' }],
      [
        'CHANGE_WASTE_HAZ',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_WASTE_HAZ')),
              Number(builder.getKeyStore('YEAR_WASTE_HAZ'))
            ),
        },
      ],
      ['PREV_YEAR_WASTE_NON_HAZ', { text: async () => builder.getKeyStore('PREV_YEAR_WASTE_NON_HAZ') ?? '-' }],
      ['YEAR_WASTE_NON_HAZ', { text: async () => builder.getKeyStore('YEAR_WASTE_NON_HAZ') ?? '-' }],
      [
        'CHANGE_WASTE_NON_HAZ',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_WASTE_NON_HAZ')),
              Number(builder.getKeyStore('YEAR_WASTE_NON_HAZ'))
            ),
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => ({
            series: [
              {
                label: 'Hazardous waste',
              },
              {
                label: 'Non-Hazardous waste',
              },
            ],
            categories: [
              {
                label: String(await builder.getYear(-1)),
                values: [
                  Number(builder.getKeyStore('PREV_YEAR_WASTE_HAZ')),
                  Number(builder.getKeyStore('PREV_YEAR_WASTE_NON_HAZ')),
                ],
              },
              {
                label: String(await builder.getYear()),
                values: [
                  Number(builder.getKeyStore('YEAR_WASTE_HAZ')),
                  Number(builder.getKeyStore('YEAR_WASTE_NON_HAZ')),
                ],
              },
            ],
          }),
        },
      ],
    ],
  },
];
