/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

const MAX_CERTIFICATIONS = 8;

export const getPPTXConfigGovernance = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Governance
    slideId: 26,
  },
  {
    // Board profile
    slideId: 27,
    appendix: async () => ['sgx-custom-42', 'gri/2020/102-22', 'gri/2020/405-1/a'],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const boardComposition = await builder.createUTRTableBuilder('sgx-custom-42').toJSONString();
            const boardComposition2 = await builder.createUTRTableBuilder('gri/2020/102-22').toJSONString();
            const boardComposition3 = await builder.createUTRTableBuilder('gri/2020/405-1/a').toJSONString();
            return builder
              .createAIBuilder()
              .ask([
                `As a company diversity and equal opportunities are important to me.`,
                `Here is information the composition of the company board:`,
                ...(boardComposition ? ['', boardComposition, ''] : []),
                ...(boardComposition2 ? ['', boardComposition2, ''] : []),
                ...(boardComposition3 ? ['', boardComposition3, ''] : []),
                `Write a narrative about this but totally ignore any information about gender.`,
              ])
              .addTarget('sgx-custom-42')
              .addTarget('gri/2020/102-22')
              .addTarget('gri/2020/405-1/a')
              .addFurtherExplanation('sgx-custom-42')
              .addFurtherExplanation('gri/2020/102-22')
              .addFurtherExplanation('gri/2020/405-1/a')
              .max(200)
              .exec();
          },
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const boardComposition = await builder.createUTRTableBuilder('sgx-custom-42').toJSONString();
            const boardComposition2 = await builder.createUTRTableBuilder('gri/2020/102-22').toJSONString();
            const boardComposition3 = await builder.createUTRTableBuilder('gri/2020/405-1/a').toJSONString();
            return builder
              .createAIBuilder()
              .ask([
                `As a company diversity and equal opportunities are important to me.`,
                `Here is information the gender composition of the company board:`,
                ...(boardComposition ? ['', boardComposition, ''] : []),
                ...(boardComposition2 ? ['', boardComposition2, ''] : []),
                ...(boardComposition3 ? ['', boardComposition3, ''] : []),
                `Write a narrative about this but totally ignore any information that isn't about gender.`,
              ])
              .addTarget('sgx-custom-42')
              .addTarget('gri/2020/102-22')
              .addTarget('gri/2020/405-1/a')
              .addFurtherExplanation('sgx-custom-42')
              .addFurtherExplanation('gri/2020/102-22')
              .addFurtherExplanation('gri/2020/405-1/a')
              .max(100)
              .exec();
          },
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART_1',
        {
          chartData: async () => {
            const male =
              (await builder.createUTRTableBuilder('gri/2020/405-1/a').multiply('number_members', 'male'));
            const female =
              (await builder.createUTRTableBuilder('gri/2020/405-1/a').multiply('number_members', 'female'));
            return {
              series: [
                {
                  label: String(await builder.getYear()),
                },
              ],
              categories: [
                {
                  label: 'Male',
                  values: [male],
                },
                {
                  label: 'Female',
                  values: [female],
                },
              ],
            };
          },
        },
      ],
    ],
  },
  {
    // Stakeholder engagement & anti-corruption
    slideId: 28,
    appendix: async () => [
      'sgx-core-28y',
      'gri/2020/205-1/b',
      'gri/2020/205-2/b',
      'gri/2020/205-3/a',
      'gri/2020/205-2/e',
    ],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const stakeholderEngagement = await builder.createUTRBuilder('sgx-core-28y').getAsString();
            return builder
              .createAIBuilder()
              .ask(
                stakeholderEngagement
                  ? [
                      `As a company stakeholder engagement is vital. `,
                      `Here is information about it: `,
                      stakeholderEngagement ?? 'No information available',
                    ]
                  : 'As a company I have not reported my stakeholder engagement practices.'
              )
              .addFurtherExplanation('sgx-core-28y')
              .narrative()
              .max(150)
              .exec();
          },
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const antiCorruptionMeasures1 = await builder.createUTRBuilder('gri/2020/205-1/b').getAsString();
            const antiCorruptionMeasures2 = await builder.createUTRTableBuilder('gri/2020/205-2/b').toJSONString();
            const antiCorruptionMeasures3 = await builder.createUTRTableBuilder('gri/2020/205-3/a').toJSONString();
            const antiCorruptionMeasures4 = await builder.createUTRTableBuilder('gri/2020/205-2/e').toJSONString();
            return builder
              .createAIBuilder()
              .ask([`As a company anti-corruption is an important part of our good governance measures.`])
              .appendConditional(
                `Here is information about our anti-corruption measures:`,
                !!antiCorruptionMeasures1 ||
                  !!antiCorruptionMeasures2 ||
                  !!antiCorruptionMeasures3 ||
                  !!antiCorruptionMeasures4
              )
              .appendConditional(antiCorruptionMeasures1)
              .appendConditional(antiCorruptionMeasures2)
              .appendConditional(antiCorruptionMeasures3)
              .appendConditional(antiCorruptionMeasures4)
              .addFurtherExplanation('gri/2020/205-1/b')
              .addFurtherExplanation('gri/2020/205-2/b')
              .addFurtherExplanation('gri/2020/205-3/a')
              .addFurtherExplanation('gri/2020/205-2/e')
              .narrative()
              .max(150)
              .min(100)
              .exec();
          },
        },
      ],
    ],
  },
  {
    // List of certifications
    slideId: 29,
    appendix: async () => ['sgx-custom-48'],
    slideRepeat: async () => {
      const table = await builder.createUTRTableBuilder('sgx-custom-48').getAsTable(['sgx48-name', 'sgx48-body']);
      if (table && table.length > MAX_CERTIFICATIONS) {
        return Math.ceil(table.length / MAX_CERTIFICATIONS);
      }
      return 1;
    },
    tableReplacements: [
      [
        'CERT_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const table = await builder.createUTRTableBuilder('sgx-custom-48').getAsTable(['sgx48-name', 'sgx48-body']);
            if (!table) {
              return null;
            }
            const offset = (repeatNum - 1) * MAX_CERTIFICATIONS;
            return [
              { values: ['Certification name', 'Certifying body'] },
              ...table.slice(offset, offset + MAX_CERTIFICATIONS),
            ];
          },
        },
      ],
    ],
  },
];
