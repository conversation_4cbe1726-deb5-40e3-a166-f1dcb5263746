/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import {
  PPTXTemplateLayoutItem,
} from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

const MAX_ROWS = 12;
const GRI_MAX_ROWS = 6;

export const getPPTXConfigAppendix = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Appendix
    slideId: 32,
  },
  {
    // Appendix 1: SGX index
    slideId: 33,
    skip: async () => builder.getAppendixTable('sgx_metrics') === null,
    slideRepeat: async () => {
      const appendixTable = builder.getAppendixTable('sgx_metrics');
      if (appendixTable && appendixTable.length > MAX_ROWS) {
        return Math.ceil(appendixTable.length / MAX_ROWS);
      }
      return 1;
    },
    tableReplacements: [
      [
        'APPENDIX_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('sgx_metrics');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * MAX_ROWS;
            return [
              { values: ['SGX Code(s)', 'Slide number'] },
              ...appendixTable.slice(offset, offset + MAX_ROWS),
            ];
          },
        },
      ],
    ],
  },
  {
    // Appendix 2: GRI index
    slideId: 34,
    skip: async () => builder.getAppendixTable('gri2021') === null,
    slideRepeat: async () => {
      const appendixTable = builder.getAppendixTable('gri2021');
      if (appendixTable && appendixTable.length > GRI_MAX_ROWS) {
        return Math.ceil(appendixTable.length / GRI_MAX_ROWS);
      }
      return 1;
    },
    tableReplacements: [
      [
        'APPENDIX_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('gri2021');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * GRI_MAX_ROWS;
            return [
              { values: ['GRI Material Topics 2021', 'GRI Code(s)', 'Slide number'] },
              ...appendixTable.slice(offset, offset + GRI_MAX_ROWS),
            ];
          },
        },
      ],
    ],
  },
  {
    // Appendix 3: SDG target alignment
    slideId: 35,
    skip: async () => builder.getAppendixTable('sdg') === null,
    slideRepeat: async () => {
      const appendixTable = builder.getAppendixTable('sdg');
      if (appendixTable && appendixTable.length > MAX_ROWS) {
        return Math.ceil(appendixTable.length / MAX_ROWS);
      }
      return 1;
    },
    tableReplacements: [
      [
        'APPENDIX_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('sdg');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * MAX_ROWS;
            return [
              { values: ['SDG', 'SDG targets', 'Slide number'] },
              ...appendixTable.slice(offset, offset + MAX_ROWS),
            ];
          },
        },
      ],
    ],
  },
  {
    // Appendix 4: SASB index
    slideId: 36,
    skip: async () => builder.getAppendixTable('sasb') === null,
    slideRepeat: async () => {
      const appendixTable = builder.getAppendixTable('sasb');
      if (appendixTable && appendixTable.length > MAX_ROWS) {
        return Math.ceil(appendixTable.length / MAX_ROWS);
      }
      return 1;
    },
    tableReplacements: [
      [
        'APPENDIX_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('sasb');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * MAX_ROWS;
            return [
              { values: ['SASB', 'Slide number'] },
              ...appendixTable.slice(offset, offset + MAX_ROWS),
            ];
          },
        },
      ],
    ],
  },
  {
    // Appendix 5: TCFD index
    slideId: 37,
    skip: async () => builder.getAppendixTable('tcfd_standard') === null,
    slideRepeat: async () => {
      const appendixTable = builder.getAppendixTable('tcfd_standard');
      if (appendixTable && appendixTable.length > MAX_ROWS) {
        return Math.ceil(appendixTable.length / MAX_ROWS);
      }
      return 1;
    },
    tableReplacements: [
      [
        'APPENDIX_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('tcfd_standard');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * MAX_ROWS;
            return [
              { values: ['TCFD', 'Slide number'] },
              ...appendixTable.slice(offset, offset + MAX_ROWS),
            ];
          },
        },
      ],
    ],
  },
];
