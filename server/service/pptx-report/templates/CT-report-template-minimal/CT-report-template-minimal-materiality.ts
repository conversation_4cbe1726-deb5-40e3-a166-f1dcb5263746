/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

const MATERIAL_FACTORS_MAX_LENGTH = 115;

const isRewriteNeeded = (text: string) => text.trim().split(/\s+/).length > MATERIAL_FACTORS_MAX_LENGTH;

export const getPPTXConfigMateriality = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Materiality
    slideId: 11,
  },
  {
    // Mission & materiality
    slideId: 12,
    appendix: async () => ['sgx-core-28x'],
    textReplacements: [
      ['COLUMN_1_TITLE', { text: async () => `Mission` }],
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const companyName = await builder.getCompanyName();
            return builder
              .createAIBuilder()
              .ask([
                `Guess what "${companyName}"'s mission statement might be.`,
                `Be unequivocal.`,
                `Don't provide explanation or excuses.`,
                `Don't use quote marks.`,
              ])
              .max(100)
              .exec();
          },
        },
      ],

      ['COLUMN_2_TITLE', { text: async () => `Materiality` }],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const text = await builder.createUTRBuilder('sgx-core-28x').getAsString();
            if (!text) {
              return builder.noInformation();
            }
            return builder.createAIBuilder().rewrite(text).max(200).exec();
          },
        },
      ],
    ],
  },
  {
    // Material factors
    slideRepeat: async () => {
      const factors = await builder.createUTRTableBuilder('sgx-custom-75').getAsTable(['sgx75-board']);
      return factors?.length ?? 0;
    },
    appendix: async () => ['sgx-custom-75'],
    slideId: 13,
    textReplacements: [
      [
        'SUBTITLE',
        {
          text: async ({ repeatNum = 1 }) =>
            builder.createUTRTableBuilder('sgx-custom-75').filterByRow(repeatNum - 1).getAsString('sgx75-board'),
        },
      ],
      ['COLUMN_1_TITLE', { text: async () => `Policies, Practices and Performance` }],
      [
        'COLUMN_1_BODY',
        {
          text: async ({ repeatNum = 1 }) => {
            const text = await builder
              .createUTRTableBuilder('sgx-custom-75')
              .filterByRow(repeatNum - 1)
              .getAsString('sgx75-policies, practices and performance');
            if (!text) {
              return builder.noInformation();
            }
            return isRewriteNeeded(text) ? builder.createAIBuilder().rewrite(text).max(MATERIAL_FACTORS_MAX_LENGTH).exec() : text;
          },
        },
      ],

      ['COLUMN_2_TITLE', { text: async () => `Targets` }],
      [
        'COLUMN_2_BODY',
        {
          text: async ({ repeatNum = 1 }) => {
            const text =
              (await builder
                .createUTRTableBuilder('sgx-custom-75')
                .filterByRow(repeatNum - 1)
                .getAsString('sgx75-targets ')) ?? 'None listed';
            return isRewriteNeeded(text) ? builder.createAIBuilder().rewrite(text).max(MATERIAL_FACTORS_MAX_LENGTH).exec() : text;
          }
        },
      ],

      ['COLUMN_3_TITLE', { text: async () => `Risks and Opportunities` }],
      [
        'COLUMN_3_BODY',
        {
          text: async ({ repeatNum = 1 }) => {
            const text = await builder
              .createUTRTableBuilder('sgx-custom-75')
              .filterByRow(repeatNum - 1)
              .getAsString('sgx75-risks and opportunities ');
            if (!text) {
              return builder.noInformation();
            }
            return isRewriteNeeded(text) ? builder.createAIBuilder().rewrite(text).max(MATERIAL_FACTORS_MAX_LENGTH).exec() : text;
          },
        },
      ],
    ],
  }
];
