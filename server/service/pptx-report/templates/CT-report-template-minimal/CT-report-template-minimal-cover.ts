/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const getPPTXConfigCover = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Title
    slideId: 1,
    imageReplacements: [['COMPANY_LOGO', { imageUrl: () => builder.getCompanyLogoUrl() }]],
  },
  {
    // Disclaimer
    slideId: 31,
  },
  // { // Contents
  //   textReplacements: [
  //     ['PAGE_BOARD', { text: async () => 'PAGE 3' }],
  //     ['PAGE_ABOUT', { text: async () => 'PAGE 4' }],
  //     ['PAGE_ABOUT_1', { text: async () => 'PAGE 5' }],
  //     ['PAGE_ABOUT_2', { text: async () => 'PAGE 6' }],
  //     ['PAGE_ABOUT_3', { text: async () => 'PAGE 7' }],
  //     ['PAGE_HIGHLIGHTS', { text: async () => 'PAGE 8' }],
  //     ['PAGE_ALIGNMENT', { text: async () => 'PAGE 9' }],
  //     ['PAGE_MISSION', { text: async () => 'PAGE 10' }],
  //     ['PAGE_MISSION_1', { text: async () => 'PAGE 11' }],
  //     ['PAGE_MISSION_2', { text: async () => 'PAGE 12' }],
  //     ['PAGE_MISSION_3', { text: async () => 'PAGE 13' }],
  //     ['PAGE_MISSION_4', { text: async () => 'PAGE 14' }],
  //     ['PAGE_MISSION_5', { text: async () => 'PAGE 15' }],
  //     ['PAGE_SOCIAL', { text: async () => 'PAGE 16' }],
  //     ['PAGE_SOCIAL_1', { text: async () => 'PAGE 1' }],
  //     ['PAGE_ENV', { text: async () => 'PAGE 1' }],
  //     ['PAGE_ENV_1', { text: async () => 'PAGE 1' }],
  //     ['PAGE_ECON', { text: async () => 'PAGE 1' }],
  //     ['PAGE_ECON_1', { text: async () => 'PAGE 1' }],
  //     ['PAGE_GOV', { text: async () => 'PAGE 1' }],
  //     ['PAGE_GOV_1', { text: async () => 'PAGE 1' }],
  //     ['PAGE_TCFD', { text: async () => 'PAGE 1' }],
  //     ['PAGE_GRI', { text: async () => 'PAGE 1' }],
  //   ],
  // },
  {
    // Board statement
    // @TODO - if board statement is longer than 400 words, then split it into two slides
    slideId: 3,
    skip: async () => !(await builder.createUTRBuilder('sgx-core-28b').hasValue()),
    appendix: async () => ['sgx-core-28b'],
    textReplacements: [
      [
        'COLUMN_1',
        {
          text: async () => {
            const text = await builder.createUTRBuilder('sgx-core-28b').getAsString();
            if (!text) {
              return builder.noInformation();
            }
            builder.setKeyStore('IntroText', text);
            const words = text.split(' ') ?? [];
            return words.slice(0, 200).join(' ');
          },
        },
      ],
      [
        'COLUMN_2',
        {
          text: async () => {
            const text = builder.getKeyStore('IntroText');
            if (!text) {
              return builder.noInformation();
            }
            const words = String(text)?.split(' ') ?? [];
            return words.slice(200).join(' ');
          },
        },
      ],
      ['AUTHOR_NAME', { text: async () => '<Type author name here>' }],
      ['AUTHOR_TITLE', { text: async () => '<Type author title here>' }],
    ],
  },
];
