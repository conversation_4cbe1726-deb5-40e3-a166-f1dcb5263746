/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const getPPTXConfigAbout = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // About
    slideId: 4,
  },
  {
    // About the company
    slideId: 5,
    textReplacements: [
      [
        'ABOUT_THE_COMPANY',
        {
          text: async () => {
            const company = await builder.getCompanyInfo();
            let prompt = `Write about ${company.name}`;
            if (company.industry) {
              prompt += `The company is a ${company.industry} company.`;
            }
            if (company.missionStatement) {
              prompt += `The company's mission is to ${company.missionStatement}.`;
            }
            if (company.geoLocation) {
              prompt += `The company is located in ${company.geoLocation}.`;
            }
            if (company.description) {
              prompt += `The company's description is: ${company.description}.`;
            }
            return builder.createAIBuilder().ask(prompt).and(``).max(200).exec();
          },
        },
      ],
    ],
    imageReplacements: [['COMPANY_LOGO', { imageUrl: () => builder.getCompanyLogoUrl() }]],
  },
  {
    // About this report 1
    slideId: 6,
    appendix: async () => ['sgx-custom-49'],
    textReplacements: [
      [
        'COLUMN_1_TITLE',
        {
          text: async () => `Reporting scope and standards`,
        },
      ],
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const text = await builder.createUTRBuilder('sgx-custom-49').getAsString();
            if (!text) {
              return builder.noInformation();
            }
            return builder.createAIBuilder().rewrite(text).max(150).exec();
          },
        },
      ],
      [
        'COLUMN_2_TITLE',
        {
          text: async () => `Reporting period`,
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const start = await builder.getReportingPeriodStart();
            const end = await builder.getReportingPeriodEnd();
            const year = await builder.getYear();
            return (
              `The Report covers the financial period ended ${end} (“FY${year}”) and highlights the activities on the company's ` +
              `business operations commencing from ${start} to ${end} unless otherwise stated. For selected performance indicators ` +
              `that have been historically tracked, we have included data from the past three years.`
            );
          },
        },
      ],
    ],
  },
  {
    // About this report 2
    slideId: 7,
    appendix: async () => ['sgx-custom-53', 'gri/2020/102-56/b'],
    textReplacements: [
      [
        'COLUMN_1_TITLE',
        {
          text: async () => `Assurance`,
        },
      ],
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const output = [];
            const part1 = await builder.createUTRTableBuilder('sgx-custom-53').getAsString('sgx53-assurance');
            const part2 = await builder.createUTRTableBuilder('sgx-custom-53').get('sgx53-describe');
            const part3 = await builder.createUTRBuilder('gri/2020/102-56/b').getAsString();
            if (part1) {
              output.push(`Level of Assurance: ${part1}`);
            }
            if (part2) {
              output.push(part2);
            }
            if (part2) {
              output.push(part3);
            }
            if (output.length === 0) {
              return builder.noInformation();
            }
            return output.join('\n\n');
          },
        },
      ],
      [
        'COLUMN_2_TITLE',
        {
          text: async () => `Report feedback`,
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const companyName = await builder.getCompanyName();
            return (
              `${companyName} welcomes feedback on our Sustainability Report. Kindly contact us about any feedback you might have. ` +
              `Full contact details are available on our website.`
            );
          },
        },
      ],
    ],
  },
  // { // Highlights
  // },
  // { // Sustainability highlights
  //   textReplacements: [
  //     ['COLUMN_1_TITLE', { text: async () => `SDG 3 Contribution has increased` }],
  //     ['COLUMN_1_BODY', {
  //       text: async (builder) => builder.getLoremIpsum(100)
  //     }],
  //     ['COLUMN_2_TITLE', { text: async () => `Waste generation is down` }],
  //     ['COLUMN_2_BODY', {
  //       text: async (builder) => builder.getLoremIpsum(100)
  //     }],
  //     ['COLUMN_3_TITLE', { text: async () => `Energy consumption is ahead of target` }],
  //     ['COLUMN_3_BODY', {
  //       text: async (builder) => builder.getLoremIpsum(100)
  //     }],
  //   ]
  // },
];
