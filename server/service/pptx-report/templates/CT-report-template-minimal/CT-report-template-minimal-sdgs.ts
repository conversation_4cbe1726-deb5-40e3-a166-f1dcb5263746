/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateImageReplacement, PPTXTemplateLayoutItem, PPTXTemplateReplacement } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const getPPTXConfigSDGs = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // SDGs
    slideId: 8,
  },
  {
    // Alignment with the SDGs
    slideId: 9,
    textReplacements: [
      [
        'AI_SDG_SUMMARY',
        {
          text: async () => {
            const score = ((await builder.getScorecardScore()) ?? 0).toFixed(1);
            const companyName = await builder.getCompanyName();
            return builder
              .createAIBuilder()
              .ask([
                `As the company called ${companyName}, we achieved a score of ${score}% for our overall SDG contribution.`,
                `Write a short narrative in first person plural form about this.`,
              ])
              .max(25)
              .exec();
          },
        },
      ],
      [
        'AI_SDG_MOST_IMPORTANT',
        {
          text: async () => {
            const companyName = await builder.getCompanyName();
            return builder
              .createAIBuilder()
              .ask([
                `As the company called ${companyName}, what is my most important SDG? Answer in first person plural form.`,
              ])
              .max(50)
              .exec();
          },
        },
      ],
      [
        'SDG_SCORE',
        {
          text: async () => {
            return builder.getScorecardScore().then((n) => (n === null ? '0' : n.toFixed(1)));
          },
        },
      ],
    ],
  },
  {
    // Alignment with the SDGs
    slideId: 10,
    textReplacements: [1, 2, 3, 4, 5, 6]
        .map<PPTXTemplateReplacement[]>((order) => [
          [
            `${order}_SDG_TITLE`,
            {
              text: async () => {
                const sdg = await builder.getSDGByMaterialityOrder(order);
                return sdg?.officialName ?? `SDG`;
              },
            },
          ],
          [
            `${order}_SDG_BODY`,
            {
              text: async () => {
                const sdg = await builder.getSDGByMaterialityOrder(order);
                if (!sdg) return builder.noInformation();
                return builder
                  .createAIBuilder()
                  .ask([
                    `As a company I have completed a Sustainability report and have achieved a ` +
                      `score of ${sdg.actual?.toFixed(1) ?? 0}% out of perfect score of 100% for SDG ${sdg.sdgCode}. `,
                  ])
                  .narrative()
                  .bePositive()
                  .max(40)
                  .and("Don't say where the data comes from.")
                  .exec();
              },
            },
          ],
        ])
        .flat(),
    imageReplacements: [
      ...(<[number, number | null, number | null][]>[
        /**
         * The SDG 1-6 icons are placed at the positions specified in the array.
         * The x and y coordinates are in centimeters,
         * and can be determined by inspecting the position of the shapes in the template.
         * */
        [1, 3.43, 7.8],
        [2, 3.43, 14.53],
        [3, 3.43, 20.93],
        [4, 25.78, 7.57],
        [5, 25.78, 14.3],
        [6, 25.78, 21],
        [7, null, null], // Remove from output
        [8, null, null], // Remove from output
        [9, null, null], // Remove from output
        [10, null, null], // Remove from output
        [11, null, null], // Remove from output
        [12, null, null], // Remove from output
        [13, null, null], // Remove from output
        [14, null, null], // Remove from output
        [15, null, null], // Remove from output
        [16, null, null], // Remove from output
        [17, null, null], // Remove from output
      ])
        .map<PPTXTemplateReplacement<PPTXTemplateImageReplacement>[]>(([order, x, y]) => [
          [
            `FAKE_${order}`,
            {
              replace: async () => {
                const sdg = await builder.getSDGByMaterialityOrder(order);
                return sdg
                  ? {
                      elementId: `ICON_SDG${sdg.sdgCode}`,
                      x,
                      y,
                    }
                  : null;
              },
            },
          ],
        ])
        .flat(),
      ['REPLACE_1', { replace: async () => null }], // Remove from output
      ['REPLACE_2', { replace: async () => null }], // Remove from output
      ['REPLACE_3', { replace: async () => null }], // Remove from output
      ['REPLACE_4', { replace: async () => null }], // Remove from output
      ['REPLACE_5', { replace: async () => null }], // Remove from output
      ['REPLACE_6', { replace: async () => null }], // Remove from output
    ],
  },
];
