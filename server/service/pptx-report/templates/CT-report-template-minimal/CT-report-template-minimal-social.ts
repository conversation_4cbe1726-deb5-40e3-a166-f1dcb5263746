/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const getPPTXConfigSocial = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Social
    slideId: 21,
  },
  {
    // Staff profile: Gender
    slideId: 22,
    appendix: async () => ['gri/2020/102-8/a', 'gri/2020/404-1/a'],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const staffHires = await builder.createUTRTableBuilder('gri/2020/102-8/a').toJSONString();
            return builder
              .createAIBuilder()
              .ask(
                staffHires
                  ? [`As a company my staff numbers are as follows:`, '', staffHires, '']
                  : 'As a company I have not reported my staff numbers.'
              )
              .addTarget('gri/2020/102-8/a')
              .addFurtherExplanation('gri/2020/102-8/a')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const avgTrainingHours = await builder.createUTRTableBuilder('gri/2020/404-1/a').toJSONString();
            return builder
              .createAIBuilder()
              .ask(
                avgTrainingHours
                  ? [
                      `As a company my staff receive the following average training hours:`,
                      '',
                      avgTrainingHours,
                      '',
                      'Write a narrative about this but only focus on gender breakdown.',
                    ]
                  : 'As a company I have not reported my staff training numbers.'
              )
              .addTarget('gri/2020/404-1/a')
              .addFurtherExplanation('gri/2020/404-1/a')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART_1',
        {
          chartData: async () => {
            const categories = [];
            const year1 = await builder.getYear(-1);
            if (year1) {
              categories.push({
                label: String(year1),
                values: [
                  await builder
                    .createUTRTableBuilder('gri/2020/102-8/a')
                    .periodOffset(-1)
                    .filterByColumn('employees_contract_gender', ['male1'])
                    .sum(['employees_contract_permanent', 'employees_contract_temporary']),
                  await builder
                    .createUTRTableBuilder('gri/2020/102-8/a')
                    .periodOffset(-1)
                    .filterByColumn('employees_contract_gender', ['females1'])
                    .sum(['employees_contract_permanent', 'employees_contract_temporary']),
                ],
              });
            }

            categories.push({
              label: String(await builder.getYear()),
              values: [
                await builder
                  .createUTRTableBuilder('gri/2020/102-8/a')
                  .filterByColumn('employees_contract_gender', ['male1'])
                  .sum(['employees_contract_permanent', 'employees_contract_temporary']),
                await builder
                  .createUTRTableBuilder('gri/2020/102-8/a')
                  .filterByColumn('employees_contract_gender', ['females1'])
                  .sum(['employees_contract_permanent', 'employees_contract_temporary']),
              ],
            });

            return {
              series: [
                {
                  label: 'Male',
                },
                {
                  label: 'Female',
                },
              ],
              categories,
            };
          },
        },
      ],
      [
        'CHART_2',
        {
          chartData: async () => {
            const categories = [];
            const year1 = await builder.getYear(-1);
            if (year1) {
              categories.push({
                label: String(year1),
                values: [
                  await builder.createUTRTableBuilder('gri/2020/404-1/a').periodOffset(-1).getAsNumber('male'),
                  await builder.createUTRTableBuilder('gri/2020/404-1/a').periodOffset(-1).getAsNumber('female'),
                ],
              });
            }

            categories.push({
              label: String(await builder.getYear()),
              values: [
                await builder.createUTRTableBuilder('gri/2020/404-1/a').getAsNumber('male'),
                await builder.createUTRTableBuilder('gri/2020/404-1/a').getAsNumber('female'),
              ],
            });

            return {
              series: [
                {
                  label: 'Male',
                },
                {
                  label: 'Female',
                },
              ],
              categories,
            };
          },
        },
      ],
    ],
  },
  {
    // Staff profile: Gender
    slideId: 23,
    appendix: async () => ['gri/2020/401-1/a'],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const hires = await builder.createUTRTableBuilder('gri/2020/401-1/a').toJSONString();
            const response = await builder
              .createAIBuilder()
              .ask(
                hires
                  ? [
                      `As a company my new staff hires in JSON format are:`,
                      hires,
                      `Write a narrative about this but focus on the gender breakdown.`,
                    ]
                  : 'As a company I have not reported my new staff hires.'
              )
              .addTarget('gri/2020/401-1/a')
              .addFurtherExplanation('gri/2020/401-1/a')
              .and(
                'Be mindful not to use language that would be contravene gender and discrimination policies in your narrative.'
              )
              .max(300)
              .exec();
            builder.setKeyStore('NewHires', response);
            const words = response.split(' ') ?? [];
            return words.slice(0, 100).join(' ');
          },
        },
      ],
      [
        'COLUMN_2_TITLE',
        {
          text: async () => {
            const text = builder.getKeyStore('NewHires');
            return text ? 'Disability' : '';
          },
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const text = builder.getKeyStore('NewHires');
            if (!text) {
              return '';
            }
            const words = String(text)?.split(' ') ?? [];
            return words.slice(100).join(' ');
          },
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART_1',
        {
          chartData: async () => ({
            series: [
              {
                label: 'Male',
              },
              {
                label: 'Female',
              },
            ],
            categories: [
              {
                label: String(await builder.getYear(-1)),
                values: [
                  await builder
                    .createUTRTableBuilder('gri/2020/401-1/a')
                    .periodOffset(-1)
                    .filterByColumn('gender', ['male1'])
                    .sum(['total_number_new_hires']),
                  await builder
                    .createUTRTableBuilder('gri/2020/401-1/a')
                    .periodOffset(-1)
                    .filterByColumn('gender', ['females1'])
                    .sum(['total_number_new_hires']),
                ],
              },
              {
                label: String(await builder.getYear()),
                values: [
                  await builder
                    .createUTRTableBuilder('gri/2020/401-1/a')
                    .filterByColumn('gender', ['male1'])
                    .sum(['total_number_new_hires']),
                  await builder
                    .createUTRTableBuilder('gri/2020/401-1/a')
                    .filterByColumn('gender', ['females1'])
                    .sum(['total_number_new_hires']),
                ],
              },
            ],
          }),
        },
      ],
    ],
  },
  {
    // Staff profile: Age
    slideId: 24,
    appendix: async () => ['gri/2020/405-1/b', 'gri/2020/401-1/b', 'gri/2020/401-1/a'],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const employees = await builder.createUTRTableBuilder('gri/2020/405-1/b').sum(['employees_per_category']);
            const employeeTurnover = await builder.createUTRTableBuilder('gri/2020/401-1/b').sum(['total_turnover']);
            return builder
              .createAIBuilder()
              .ask([
                `As a company aged based diversity is important to me.`,
                `Here is all the information on age based diversity at the company:`,
                '',
                `Current employees: ${employees}`,
                `Employee turnover: ${employeeTurnover}`,
                '',
              ])
              .addTarget('gri/2020/405-1/b', 'employees_per_category')
              .addTarget('gri/2020/401-1/b', 'total_turnover')
              .addFurtherExplanation('gri/2020/405-1/b')
              .addFurtherExplanation('gri/2020/401-1/b')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const hires = await builder.createUTRTableBuilder('gri/2020/401-1/a').toJSONString();
            return builder
              .createAIBuilder()
              .ask(
                hires
                  ? [
                      `As a company aged-based diversity is important to me.`,
                      `Here is some information about new hires at the company:`,
                      '',
                      hires,
                      '',
                    ]
                  : 'As a company I have not reoprted on my aged-based diversity for new hires.'
              )
              .addTarget('gri/2020/401-1/a', 'total_number_new_hires')
              .addFurtherExplanation('gri/2020/401-1/a')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_UNDER_30',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_UNDER_30',
              await builder
                .createUTRTableBuilder('gri/2020/405-1/b')
                .periodOffset(-1)
                .multiply('employees_per_category', 'under_30')
            ) ?? '',
        },
      ],
      [
        'YEAR_UNDER_30',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_UNDER_30',
              await builder.createUTRTableBuilder('gri/2020/405-1/b').multiply('employees_per_category', 'under_30')
            ) ?? '',
        },
      ],
      [
        'CHANGE_UNDER_30',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_UNDER_30')),
              Number(builder.getKeyStore('YEAR_UNDER_30'))
            ),
        },
      ],
      [
        'PREV_YEAR_30_50',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_30_50',
              await builder
                .createUTRTableBuilder('gri/2020/405-1/b')
                .periodOffset(-1)
                .multiply('employees_per_category', 'age_30_50')
            ) ?? '',
        },
      ],
      [
        'YEAR_30_50',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_30_50',
              await builder.createUTRTableBuilder('gri/2020/405-1/b').multiply('employees_per_category', 'age_30_50')
            ) ?? '',
        },
      ],
      [
        'CHANGE_30_50',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_30_50')),
              Number(builder.getKeyStore('YEAR_30_50'))
            ),
        },
      ],
      [
        'PREV_YEAR_OVER_50',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_OVER_50',
              await builder
                .createUTRTableBuilder('gri/2020/405-1/b')
                .periodOffset(-1)
                .multiply('employees_per_category', 'over_50')
            ) ?? '',
        },
      ],
      [
        'YEAR_OVER_50',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_OVER_50',
              await builder.createUTRTableBuilder('gri/2020/405-1/b').multiply('employees_per_category', 'over_50')
            ) ?? '',
        },
      ],
      [
        'CHANGE_OVER_50',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_OVER_50')),
              Number(builder.getKeyStore('YEAR_OVER_50'))
            ),
        },
      ],
      [
        'PREV_YEAR_HIRES_UNDER_30',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_HIRES_UNDER_30',
              await builder.createUTRTableBuilder('gri/2020/401-1/a').periodOffset(-1).sum(['under_30'])
            ) ?? '',
        },
      ],
      [
        'YEAR_HIRES_UNDER_30',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_HIRES_UNDER_30',
              await builder.createUTRTableBuilder('gri/2020/401-1/a').sum(['under_30'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_HIRES_UNDER_30',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_HIRES_UNDER_30')),
              Number(builder.getKeyStore('YEAR_HIRES_UNDER_30'))
            ),
        },
      ],
      [
        'PREV_YEAR_HIRES_30_50',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_HIRES_30_50',
              await builder.createUTRTableBuilder('gri/2020/401-1/a').periodOffset(-1).sum(['age_30_50'])
            ) ?? '',
        },
      ],
      [
        'YEAR_HIRES_30_50',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_HIRES_30_50',
              await builder.createUTRTableBuilder('gri/2020/401-1/a').sum(['age_30_50'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_HIRES_30_50',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_HIRES_30_50')),
              Number(builder.getKeyStore('YEAR_HIRES_30_50'))
            ),
        },
      ],
      [
        'PREV_YEAR_HIRES_OVER_50',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_HIRES_OVER_50',
              await builder.createUTRTableBuilder('gri/2020/401-1/a').periodOffset(-1).sum(['over_50'])
            ) ?? '',
        },
      ],
      [
        'YEAR_HIRES_OVER_50',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_HIRES_OVER_50',
              await builder.createUTRTableBuilder('gri/2020/401-1/a').sum(['over_50'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_HIRES_OVER_50',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_HIRES_OVER_50')),
              Number(builder.getKeyStore('YEAR_HIRES_OVER_50'))
            ),
        },
      ],
    ],
  },
  {
    // Staff
    slideId: 25,
    appendix: async () => ['gri/2020/403-9/a', 'gri/2020/403-10/a', 'gri/2020/401-1/a'],
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const injuries = await builder.createUTRTableBuilder('gri/2020/403-9/a').toJSONString();
            const healthCases = await builder.createUTRTableBuilder('gri/2020/403-10/a').toJSONString();
            return builder
              .createAIBuilder()
              .ask(
                injuries || healthCases
                  ? [
                      `As a company health and safety in the workplace is important to me.`,
                      `Here is information on Work-related injuries and fatalities:`,
                      '',
                      injuries ?? 'Not Reported',
                      '',
                      `and information on Work-related ill health cases:`,
                      '',
                      healthCases ?? 'Not Reported',
                      '',
                    ]
                  : 'As a company I have not reported on my work-related injuries of fatalities.'
              )
              .addTarget('gri/2020/403-9/a')
              .addTarget('gri/2020/403-10/a')
              .addFurtherExplanation('gri/2020/403-9/a')
              .addFurtherExplanation('gri/2020/403-10/a')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
      [
        'COLUMN_2_TITLE',
        {
          text: async () => {
            const hires = await builder.createUTRTableBuilder('gri/2020/401-1/a').toJSONString();
            return hires ? 'Disability' : '';
          },
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const hires = await builder.createUTRTableBuilder('gri/2020/401-1/a').toJSONString();
            if (!hires) {
              return '';
            }
            return builder
              .createAIBuilder()
              .ask(
                hires
                  ? [
                      `As a company aged-based diversity is important to me.`,
                      `Here is some information about new hires at the company:`,
                      '',
                      hires,
                      '',
                    ]
                  : 'As a company I have not reoprted on my aged-based diversity for new hires.'
              )
              .addTarget('gri/2020/401-1/a')
              .addFurtherExplanation('gri/2020/401-1/a')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_FATALITIES',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_FATALITIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a')
                .periodOffset(-1)
                .sum(['injuries_company_employees_number_fatalities'])
            ) ?? '',
        },
      ],
      [
        'YEAR_FATALITIES',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_FATALITIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a')
                .sum(['injuries_company_employees_number_fatalities'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_FATALITIES',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_FATALITIES')),
              Number(builder.getKeyStore('YEAR_FATALITIES'))
            ),
        },
      ],
      [
        'PREV_YEAR_HIGH_INJURIES',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_HIGH_INJURIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a')
                .periodOffset(-1)
                .sum(['injuries_company_employees_high-consequence_work-related_injuries_number'])
            ) ?? '',
        },
      ],
      [
        'YEAR_HIGH_INJURIES',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_HIGH_INJURIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a')
                .sum(['injuries_company_employees_high-consequence_work-related_injuries_number'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_HIGH_INJURIES',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_HIGH_INJURIES')),
              Number(builder.getKeyStore('YEAR_HIGH_INJURIES'))
            ),
        },
      ],
      [
        'PREV_YEAR_REC_INJURIES',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_REC_INJURIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a')
                .periodOffset(-1)
                .sum(['injuries_company_employees_recordable_injuries_number'])
            ) ?? '',
        },
      ],
      [
        'YEAR_REC_INJURIES',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_REC_INJURIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a')
                .sum(['injuries_company_employees_recordable_injuries_number'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_REC_INJURIES',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_REC_INJURIES')),
              Number(builder.getKeyStore('YEAR_REC_INJURIES'))
            ),
        },
      ],
    ],
  },
];
