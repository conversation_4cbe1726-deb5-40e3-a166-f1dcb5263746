import { RowData } from '../../../../models/public/universalTrackerValueType';
import { isNumericString } from '../../../../util/string';
import { formatNumber } from '../../../../util/number';

export const displayValueWithSuffix = (
  params:
    | {
        value: number | string | null | undefined;
        unit?: number | string;
        numberScale?: number | string;
      }
    | undefined
) => {
  const { value, unit, numberScale } = params ?? {};
  if (!value) {
    return '';
  }
  let result = value;
  if (numberScale) {
    result = `${result} ${numberScale}`;
  }
  if (unit) {
    result = `${result} ${unit}`;
  }
  return result;
};

export const getValueByColumnCode = ({
  tableData,
  columnCode,
  rowIndex = 0,
}: {
  tableData: RowData[][];
  columnCode: string;
  rowIndex?: number;
}) => {
  if (tableData.length === 0 || rowIndex > tableData.length - 1) {
    return;
  }
  return tableData[rowIndex]?.find((col) => col.code === columnCode)?.value;
};

export const getValueFromDivision = ({
  tableData,
  numeratorCode,
  denominatorCode,
  rowIndex = 0,
  decimal = 2,
}: {
  tableData: RowData[][];
  numeratorCode: string;
  denominatorCode: string;
  rowIndex?: number;
  decimal?: number;
}) => {
  const numeratorValue = getValueByColumnCode({ tableData, columnCode: numeratorCode, rowIndex });
  const denominatorValue = getValueByColumnCode({ tableData, columnCode: denominatorCode, rowIndex });
  if (!isNumericString(numeratorValue) || !isNumericString(denominatorValue) || Number(denominatorValue) === 0) {
    return;
  }
  const value = Number(numeratorValue) / Number(denominatorValue);
  return formatNumber({ value, decimal });
};
