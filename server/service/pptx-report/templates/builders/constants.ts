import { CT_STARTER_ECONOMIC_SLIDE_UTRS } from '../CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-economic';
import { CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS } from '../CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-environmental';
import { CT_STARTER_GOVERNANCE_SLIDE_UTRS } from '../CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-governance';
import { CT_STARTER_SOCIAL_SLIDE_UTRS } from '../CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-social';

export const CT_STARTER_SLIDE_UTRS: Record<
  number,
  {
    utrs: { utrCode: string; columnCodes?: string[] }[];
    previousSurveyIncluded?: boolean;
  }
> = {
  ...CT_STARTER_ECONOMIC_SLIDE_UTRS,
  ...CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS,
  ...CT_STARTER_SOCIAL_SLIDE_UTRS,
  ...CT_STARTER_GOVERNANCE_SLIDE_UTRS,
};

export enum ReportOffset {
  Current = 0,
  Previous = -1,
}
