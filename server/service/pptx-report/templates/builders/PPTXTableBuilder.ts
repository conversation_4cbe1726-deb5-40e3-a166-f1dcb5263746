/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { RowData, TableData } from '../../../../models/public/universalTrackerValueType';
import { ValueList } from '../../../../models/public/valueList';
import { OrderingDirection } from '../../../../types/ordering';
import { getTableNumberScaleCode, getTableUnitCode, getUtrvTableProp } from '../../../utr/utrvUtil';
import { generateMapKey } from '../../utils';
import { PPTXTemplateSurveyCacheManager } from '../PPTXTemplateSurveyCacheManager';
import { InputMapData, isTableInputData } from '../PPTXTemplateInterface';
import { isPercentageColumn } from '../../../../util/universal-trackers';

type QueuedFilter = (table: RowData[], ix: number) => boolean;
type CustomReducer = (acc: number, row: RowData[]) => number;

export class PPTXTableBuilder {
  private reportOffset = 0;
  private queuedFilters: QueuedFilter[] = [];
  private applyMaxDecimals: number | undefined;

  /**
   * @param repositoryManager - Manages the survey cache for PPTX templates
   * return PPTXTemplateSurveyCache get raw utrv through getUTRV()
   * will be deprecated in the future, use `utrsInputMap` to get value instead
   * @param utrCode - The unique code for the UTR (Universal Tracker Report).
   * @param utrsInputMap - Optional map supplying input data for UTRs
   * determined by unit/numberScale compatibility to return either the user input data or converted data.
   */
  public constructor(
    private repositoryManager: PPTXTemplateSurveyCacheManager,
    private utrCode: string,
    private utrsInputMap: Map<string, InputMapData> | undefined
  ) {}

  // Chaining functions

  public periodOffset(reportOffset: number) {
    this.reportOffset = reportOffset;
    return this;
  }

  public filterByRow(rowNum: number): PPTXTableBuilder {
    this.queuedFilters.push((row, ix) => {
      return ix === rowNum;
    });
    return this;
  }

  public filterByColumn(columnCode: string, match: string[]): PPTXTableBuilder {
    this.queuedFilters.push((row) => {
      const col = row.find((c) => c.code === columnCode);
      return Boolean(col && match.includes(String(col.value)));
    });
    return this;
  }

  public maxDecimals(precision: number): PPTXTableBuilder {
    this.applyMaxDecimals = precision;
    return this;
  }

  // End of line functions

  private async applyFilters(): Promise<TableData | null> {
    const tableData = await this.getTableData();
    if (!tableData) {
      return null;
    }

    return this.queuedFilters.reduce((filteredTable, queuedFilter) => {
      return filteredTable.filter(queuedFilter);
    }, tableData);
  }

  public async get(columnCode: string): Promise<string | number | string[] | undefined> {
    const table = await this.applyFilters();
    if (!table || table.length === 0) {
      return undefined;
    }
    const value = table[0].find((c) => c.code === columnCode)?.value;
    if (value === undefined) {
      return undefined;
    }

    return this.resolveValueList(columnCode, value);
  }

  private async getTableData() {
    if (this.utrsInputMap) {
      const utrMapKey = generateMapKey(this.utrCode, this.reportOffset);
      const inputs = this.utrsInputMap.get(utrMapKey);
      if (!inputs || !isTableInputData(inputs)) {
        return [];
      }
      return inputs;
    }
    const utrv = await this.repositoryManager.getCachedSurvey(this.reportOffset)?.getUTRV(this.utrCode);
    if (!utrv) {
      return null;
    }
    return getUtrvTableProp(utrv, true);
  }

  public async getInputNumericAnswer(columnCode: string) {
    const table = await this.applyFilters();
    if (!table || table.length === 0) {
      return undefined;
    }

    
    const rowData = table[0].find((c) => c.code === columnCode);
    const value = rowData?.value;

    if (!value) {
      return undefined;
    }

    const utrv = await this.repositoryManager.getCachedSurvey(this.reportOffset)?.getUTRV(this.utrCode);
    const column = utrv?.universalTracker?.valueValidation?.table?.columns?.find((c) => c.code === columnCode);

    if (!column) { 
      return undefined;
    }

    const unit = getTableUnitCode(column, rowData, { displayUserInput: true });
    const numberScale = getTableNumberScaleCode(column, rowData, { displayUserInput: true });

    return {
      value: await this.resolveValueList(columnCode, value),
      unit,
      numberScale
    };
  }

  private async resolveValueList(columnCode: string, value: string | number): Promise<string | number> {
    const utrv = await this.repositoryManager.getCachedSurvey(this.reportOffset)?.getUTRV(this.utrCode);
    const valueLists = utrv?.universalTracker?.tableColumnValueListOptions;
    const options = valueLists?.find((v) => v.name === columnCode)?.options;
    const option = options?.find((o) => o.code === value);
    return option?.name ?? value;
  }

  private async resolveValueListColumn(
    columnCode: string,
    value: string | number | string[],
    valueLists: ValueList[]
  ): Promise<string | number> {
    const options = valueLists.find((v) => v.name === columnCode)?.options;

    if (Array.isArray(value)) {
      return value
        .map((v) => {
          const option = options?.find((o) => o.code === v);
          return option?.name ?? v;
        })
        .join(', ');
    }

    const option = options?.find((o) => o.code === value);
    return option?.name ?? value;
  }

  public async getAsString(columnCode: string): Promise<string> {
    const value = await this.get(columnCode);
    if (value === undefined) {
      return '';
    }
    const utrv = await this.repositoryManager.getCachedSurvey(this.reportOffset)?.getUTRV(this.utrCode);
    const valueLists = utrv?.universalTracker?.tableColumnValueListOptions;
    if (valueLists && valueLists.length > 0) {
      return String(await this.resolveValueListColumn(columnCode, value, valueLists));
    }

    if (Array.isArray(value)) {
      return value.join(', ');
    }
    return String(value);
  }

  public async getRawValue(columnCode: string): Promise<number | string | undefined> {
    const value = await this.get(columnCode);
    if (value && Array.isArray(value)) {
      return value.join(', ');
    }
    return value;
  }

  public async getAsNumber(columnCode: string): Promise<number> {
    const value = await this.get(columnCode);
    return this.applyDecimals(this.toNum(value));
  }

  private applyDecimals(value: number): number {
    if (this.applyMaxDecimals !== undefined) {
      return Number(value?.toFixed(this.applyMaxDecimals));
    }
    return value;
  }

  private toNum(value: any): number {
    const v = Number(value);
    return Number.isNaN(v) ? 0 : v;
  }

  private toPercent(value: any): number {
    const num = Number(value);
    return Number.isNaN(num) ? 0 : num / 100;
  }

  private isValidTable(table: TableData | null): table is TableData {
    if (!table || table.length === 0) {
      return false;
    }
    // ensure at least one column has a value
    return table.some((row) => row.some((col) => col.value !== undefined));
  }

  public async sum(columnCodes: string[], fallback: number | '' = ''): Promise<'' | number> {
    const table = await this.applyFilters();
    if (!this.isValidTable(table)) {
      return fallback;
    }
    const sum = await this.reduce((tableAcc, cur) => {
      const rowTotal = cur.reduce((rowAcc, c) => {
        if (columnCodes.includes(c.code)) {
          return rowAcc + this.toNum(c.value);
        }
        return rowAcc;
      }, 0);
      return tableAcc + rowTotal;
    });
    return this.applyDecimals(sum === undefined ? 0 : sum);
  }

  public async getAsTable(columnCodes: string[]): Promise<
    | {
        values: (string | number)[];
      }[]
    | null
  > {
    const table = await this.applyFilters();
    if (!table || table.length === 0) {
      return null;
    }
    return table.map((row) => {
      return {
        values: columnCodes.map((code) => {
          const col = row.find((r) => r.code === code);
          // @TODO resolve list lookups
          return col?.value ?? '';
        }),
      };
    });
  }

  public async multiply(colA: string, colB: string): Promise<number> {
    const table = await this.applyFilters();
    if (!table || table.length === 0) {
      return 0;
    }

    const columns = await this.getTableColumns();

    const toNum = (row: RowData[], col: string): number => {
      const inputCol = row.find((r) => r.code === col);
      const column = columns.find((c) => c.code === col);
      return column && isPercentageColumn(column) ? this.toPercent(inputCol?.value) : this.toNum(inputCol?.value);
    };

    const sum = await this.reduce((acc, row) => {
      const colANumber = toNum(row, colA);
      const colBNumber = toNum(row, colB);
      return acc + colANumber * colBNumber;
    });
    return this.applyDecimals(sum === undefined ? 0 : sum);
  }

  public async reduce(reducer: CustomReducer): Promise<number> {
    const table = await this.applyFilters();
    if (!table || table.length === 0) {
      return 0;
    }
    const sum = table.reduce(reducer, 0);
    return sum === undefined ? 0 : sum;
  }

  public async toJSONString(): Promise<string | null> {
    const table = await this.applyFilters();
    if (!table || table.length === 0) {
      return null;
    }

    const utrv = await this.repositoryManager.getCachedSurvey(this.reportOffset)?.getUTRV(this.utrCode);
    const tableConfig = utrv?.universalTracker?.valueValidation?.table;

    const getColumnName = (code: string): string => {
      return tableConfig?.columns.find((c) => c.code === code)?.name ?? code;
    };
    const simplifiedTable = table.map((row) => {
      return row.map((col) => ({
        name: getColumnName(col.code),
        value: col.value,
      }));
    });
    return JSON.stringify(simplifiedTable);
  }

  private async getTableColumns() {
    const utrv = await this.repositoryManager.getCachedSurvey(this.reportOffset)?.getUTRV(this.utrCode);
    return utrv?.universalTracker?.valueValidation?.table?.columns ?? [];
  }

  public async getSortedTable({
    columnCode,
    direction = OrderingDirection.Desc,
    limit,
  }: {
    columnCode: string;
    direction?: OrderingDirection;
    limit?: number;
  }) {
    const table = await this.applyFilters();

    if (!table || table.length === 0) {
      return [];
    }

    const directionMultiplier = direction === OrderingDirection.Asc ? 1 : -1;

    const sortedTable = table.sort((rowA, rowB) => {
      const a = rowA.find((c) => c.code === columnCode);
      const b = rowB.find((c) => c.code === columnCode);
      if (!a || !b) {
        return 0;
      }
      const aValue = Number(a.value ?? 0);
      const bValue = Number(b.value ?? 0);
      return (aValue - bValue) * directionMultiplier;
    });

    if (!limit) {
      return sortedTable;
    }
    return sortedTable.slice(0, limit);
  }
}
