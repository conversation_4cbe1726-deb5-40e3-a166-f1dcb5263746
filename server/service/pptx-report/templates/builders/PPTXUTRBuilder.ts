/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import ContextError from '../../../../error/ContextError';
import { UtrValueType } from '../../../../models/public/universalTrackerType';
import { getNumberScaleCode, getUnitCode, getUtrvDataProp, getUtrvValue } from '../../../utr/utrvUtil';
import { LoggerInterface } from '../../../wwgLogger';
import { PPTXTemplateSurveyCacheManager } from '../PPTXTemplateSurveyCacheManager';
import { InputMapData, isInputData, isValueListInputData } from '../PPTXTemplateInterface';
import { generateMapKey } from '../../utils';
import { isNumericString } from '../../../../util/string';

const validTypes = [UtrValueType.Number, UtrValueType.Percentage];

export class PPTXUTRBuilder {
  private reportOffset = 0;
  private applyMaxDecimals: number | undefined;
  private applyFallback: number | null = null;

  public constructor(
    private logger: LoggerInterface,
    private repositoryManager: PPTXTemplateSurveyCacheManager,
    private utrCode: string,
    private utrsInputMap: Map<string, InputMapData> | undefined
  ) {}

  private async getUtrv() {
    return this.repositoryManager.getCachedSurvey(this.reportOffset)?.getUTRV(this.utrCode);
  }

  public async getUtr() {
    const utrv = await this.getUtrv();
    return utrv?.universalTracker;
  }

  private async getStandardValue() {
    const utrv = await this.getUtrv();
    if (!utrv) {
      return this.applyFallback;
    }
    const valueType = utrv.valueType ?? utrv.universalTracker?.valueType;

    if (!valueType || !validTypes.includes(valueType)) {
      this.logger.info(
        new ContextError('Attempting to get numeric value from non-numeric UTR', { utrCode: this.utrCode, valueType })
      );
      return this.applyFallback;
    }

    let value = utrv.value === undefined ? this.applyFallback : utrv.value;
    if (this.applyMaxDecimals !== undefined) {
      value = Number(value?.toFixed(this.applyMaxDecimals));
    }
    return value;
  }

  public async getInputSimpleNumericAnswer() {
    const fallback = {
      value: this.applyFallback,
      unit: undefined,
      numberScale: undefined,
    };

    if (this.utrsInputMap) {
      return this.getInputFromMap() ?? fallback;
    }

    const utrv = await this.getUtrv();

    if (!utrv || !utrv.universalTracker) {
      return fallback;
    }
    const valueType = utrv.valueType ?? utrv.universalTracker.valueType;

    if (!valueType || !validTypes.includes(valueType)) {
      this.logger.info(
        new ContextError('Attempting to get numeric value from non-numeric UTR', { utrCode: this.utrCode, valueType })
      );
      return fallback;
    }
    const displayOptions = { displayUserInput: true };
    let value = getUtrvValue(utrv, true).value ?? this.applyFallback;
    const unit = getUnitCode(utrv, utrv.universalTracker, displayOptions);
    const numberScale = getNumberScaleCode(utrv, utrv.universalTracker, displayOptions);

    if (typeof value === 'number' && this.applyMaxDecimals !== undefined) {
      value = Number(value.toFixed(this.applyMaxDecimals));
    }
    return {
      value,
      unit,
      numberScale,
    };
  }

  // Chaining functions

  public periodOffset(reportOffset: number) {
    this.reportOffset = reportOffset;
    return this;
  }

  public maxDecimals(precision: number | undefined): PPTXUTRBuilder {
    this.applyMaxDecimals = precision;
    return this;
  }

  public fallback(fallback: number | null): PPTXUTRBuilder {
    this.applyFallback = fallback;
    return this;
  }

  // End of line functions

  public async getTypeTags(type: string): Promise<string[] | undefined> {
    const utrv = await this.getUtrv();
    if (!utrv) {
      return;
    }
    if (utrv.universalTracker?.type === type) {
      return utrv.universalTracker?.typeTags;
    }
    return utrv.universalTracker?.alternatives?.[type]?.typeTags;
  }

  public async getTypeCode(type: string): Promise<string | undefined> {
    const utrv = await this.getUtrv();
    if (!utrv) {
      return;
    }
    if (utrv.universalTracker?.type === type) {
      return utrv.universalTracker?.typeCode;
    }
    return utrv.universalTracker?.alternatives?.[type]?.typeCode;
  }

  public async getTags(type: string): Promise<string[] | undefined> {
    const utrv = await this.getUtrv();
    if (!utrv) {
      return;
    }
    return utrv.universalTracker?.tags?.[type];
  }

  public async getAsNumber(): Promise<number | null> {
    if (this.utrsInputMap) {
      return this.getAsNumberFromMap();
    }
    return this.getStandardValue();
  }

  private getAsNumberFromMap() {
    const input = this.getInputFromMap();
    const value = input && isInputData(input) ? input.value : undefined;
    if (!isNumericString(value)) {
      return this.applyFallback;
    }
    if (this.applyMaxDecimals !== undefined) {
      return parseFloat(Number(value).toFixed(this.applyMaxDecimals));
    }
    return Number(value);
  }

  private getInputFromMap() {
    const utrMapKey = generateMapKey(this.utrCode, this.reportOffset);
    const input = this.utrsInputMap?.get(utrMapKey);
    if (input && !isInputData(input)) {
      return;
    }
    return input;
  }

  public async getRawValue() {
    if (this.utrsInputMap) {
      const input = this.getInputFromMap();
      return input?.value;
    }
    const utrv = await this.getUtrv();
    if (!utrv) {
      return;
    }
    return utrv.value;
  }

  public async getAsBoolean(): Promise<boolean> {
    const v = await this.getAsNumber();
    if (v !== null) {
      return true;
    }
    const s = await this.getAsString();
    if (s !== null) {
      return true;
    }
    return false;
  }

  public async hasValue(): Promise<boolean> {
    return this.getAsBoolean();
  }

  public async getAsString(): Promise<string | undefined> {
    const utrv = await this.getUtrv();
    if (!utrv) {
      return;
    }
    const lookupValueList = (value: string) => {
      const options = utrv?.universalTracker?.valueListOptions?.options;
      const option = options?.find((o) => o.code === value);
      return option?.name ?? value;
    };
    const valueType = utrv.valueType ?? utrv.universalTracker?.valueType;
    switch (valueType) {
      case UtrValueType.Date:
      case UtrValueType.Text:
        return utrv?.valueData?.data;
      case UtrValueType.NumericValueList:
      case UtrValueType.TextValueList: {
        const data = utrv?.valueData?.data as { [key: string]: string | number } | undefined;
        const keys = data ? Object.keys(data) : [];
        return keys.map((key) => `${lookupValueList(key)}: ${data?.[key] ?? '-'}`).join('\n\n');
      }
      case UtrValueType.ValueList: {
        const data = utrv?.valueData?.data as string | string[] | undefined;
        return Array.isArray(data) ? data.map(lookupValueList).join(', ') : lookupValueList(String(data));
      }
      case UtrValueType.Number:
      case UtrValueType.Percentage: {
        const value = await this.getAsNumber();
        return value ? String(value) : undefined;
      }
      default:
        this.logger.info(
          new ContextError('Attempting to get text value from non-text UTR', { utrCode: this.utrCode, valueType })
        );
        return;
    }
  }

  public async getUtrName(): Promise<string> {
    const utrv = await this.getUtrv();
    if (!utrv || !utrv.universalTracker) {
      return '';
    }
    return utrv.universalTracker.name ?? utrv.universalTracker.code;
  }

  public async getComplexValueListData(fallback = {}): Promise<{ [key: string]: number | string | undefined }> {
    if (this.utrsInputMap) {
      const utrMapKey = generateMapKey(this.utrCode, this.reportOffset);
      const inputs = this.utrsInputMap?.get(utrMapKey);
      if (!inputs || !isValueListInputData(inputs)) {
        return fallback;
      }

      return Object.fromEntries(inputs.map(({ valueListCode, value }) => [valueListCode, value]));
    }
    const utrv = await this.getUtrv();
    if (!utrv) {
      return fallback;
    }
    const displayUserInput = true;
    return getUtrvDataProp(utrv, displayUserInput)?.data ?? fallback;
  }

  public async getTotalFromNumericValueList(): Promise<number | undefined> {
    const data = await this.getComplexValueListData();
    const values = Object.values(data);
    if (!values.length) {
      return;
    }

    const total = values.reduce<number>((acc, value) => acc + (Number(value) || 0), 0);
    return this.applyMaxDecimals !== undefined ? Number(total.toFixed(this.applyMaxDecimals)) : total;
  }
}
