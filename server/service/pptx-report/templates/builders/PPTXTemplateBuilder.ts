/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { AiService } from '../../../ai/service';
import { getIndustryText } from '../../../reporting/FrameworkMapping';
import { DateFormat, customDateFormat } from '../../../../util/date';
import { LoggerInterface } from '../../../wwgLogger';
import ContextError from '../../../../error/ContextError';
import { PPTXTableBuilder } from './PPTXTableBuilder';
import { PPTXAIBuilder } from './PPTXAIBuilder';
import { PPTXTemplateSurveyCacheManager } from '../PPTXTemplateSurveyCacheManager';
import Initiative, { InitiativePlain } from '../../../../models/initiative';
import { KeysEnum } from '../../../../models/commonProperties';
import { ObjectId } from 'bson';
import { PPTXUTRBuilder } from './PPTXUTRBuilder';
import moment from 'moment';
import { RowData } from '../../../../models/public/universalTrackerValueType';
import { getReportingDateStart } from '../../../../util/survey';
import { InputMapData, SlideData } from '../PPTXTemplateInterface';
import { MetricUnitManager } from '../../../units/MetricUnitManager';
import { isNumericString } from '../../../../util/string';
import { ReportOffset } from './constants';
import { SlideSection, PPTXSlideRecommendationUtr } from './AISlideRecommendationBuilder';

type KeyStoreTypes = string | number | undefined | null;

type BuilderInitiative = Pick<
  InitiativePlain,
  '_id' | 'industry' | 'name' | 'profile' | 'missionStatement' | 'description' | 'geoLocation'
>;

type TableData = string | number;

interface TableDataRow {
  values: TableData[];
}

export class PPTXTemplateBuilder {
  private store: Map<string, KeyStoreTypes> = new Map();
  private appendixStore: Map<string, TableData[][]> = new Map();
  private tableStore: Map<string, RowData[][]> = new Map();
  private initiative: null | BuilderInitiative = null;

  constructor(
    private logger: LoggerInterface,
    private initiativeId: ObjectId,
    private repositoryManager: PPTXTemplateSurveyCacheManager,
    private aiService: AiService,
    private debugMode: boolean,
    private slidesAnswerMap: Map<number, SlideData>,
    private slidesRecommendationMap: Map<string, PPTXSlideRecommendationUtr[]> = new Map()
  ) {}

  public getSurveyCache(periodOffset: number = 0) {
    return this.repositoryManager.getCachedSurvey(periodOffset);
  }

  public getSlidesRecommendation(section: SlideSection): PPTXSlideRecommendationUtr[] {
    return this.slidesRecommendationMap.get(section) || [];
  }

  private getUtrsInputMapBySlideId(slideId: number | undefined): Map<string, InputMapData> | undefined {
    if (slideId === undefined) {
      return;
    }
    return this.slidesAnswerMap.get(slideId)?.utrsInputMap;
  }

  public getUnitAndNumberScale({ slideId, format = 'name' }: { slideId: number; format?: 'code' | 'name' | 'full' }) {
    const { unit, numberScale } = this.slidesAnswerMap.get(slideId) || {};
    const unitText = MetricUnitManager.formatUnit({ unit, format });
    return [numberScale, unitText].filter(Boolean).join(' ');
  }

  public async getInitiative() {
    if (this.initiative === null) {
      const initiativeProjection: KeysEnum<BuilderInitiative> = {
        _id: 1,
        industry: 1,
        name: 1,
        profile: 1,
        missionStatement: 1,
        description: 1,
        geoLocation: 1,
      };

      this.initiative = await Initiative.findById(this.initiativeId, initiativeProjection).lean();
      if (!this.initiative) {
        throw new ContextError('Could not load initiative to generate PPTX report', {
          context,
        });
      }
    }
    return this.initiative;
  }

  public async getYear(periodOffset: number = 0): Promise<number | null> {
    const survey = await this.getSurveyCache(periodOffset)?.getSurvey();
    if (!survey) {
      const currentYear = await this.getYear();
      if (!currentYear) {
        return null;
      }
      return currentYear + periodOffset;
    }
    return moment.utc(survey.effectiveDate).year();
  }

  public async getReportingPeriodStart(periodOffset: number = 0) {
    const survey = await this.getSurveyCache(periodOffset)?.getSurvey();
    if (!survey) {
      return null;
    }
    return getReportingDateStart(survey);
  }

  public async getReportingPeriodEnd(periodOffset: number = 0) {
    const survey = await this.getSurveyCache(periodOffset)?.getSurvey();
    if (!survey) {
      return null;
    }
    return customDateFormat(survey.effectiveDate, DateFormat.MonthYear);
  }

  public async getSectorName(): Promise<null | string> {
    const initiative = await this.getInitiative();
    if (!initiative.industry) {
      return null;
    }
    return getIndustryText(initiative.industry);
  }

  public setKeyStore(key: string, value: KeyStoreTypes) {
    this.store.set(key, value);
    return value;
  }

  public getKeyStore(key: string): KeyStoreTypes {
    return this.store.get(key);
  }

  public setTableStore(key: string, value: RowData[][]) {
    this.tableStore.set(key, value);
    return value;
  }

  public getTableStore(key: string): RowData[][] | undefined {
    return this.tableStore.get(key);
  }

  public addAppendixEntry(type: string, row: TableData[]) {
    const current = this.appendixStore.get(type);
    if (current) {
      current.push(row);
      return;
    }
    this.appendixStore.set(type, [row]);
  }

  public getAppendixTable(type: string): TableDataRow[] | null {
    const table = this.appendixStore.get(type);
    if (!table) {
      return null;
    }
    return table.map((row) => ({ values: row }));
  }

  public async getCompanyName(): Promise<string> {
    const initiative = await this.getInitiative();
    return initiative.name;
  }

  public async getCompanyLogoUrl(): Promise<string | null> {
    const initiative = await this.getInitiative();
    return initiative.profile ?? null;
  }

  public async getCompanyInfo() {
    const initiative = await this.getInitiative();

    return {
      name: initiative.name,
      description: initiative.description,
      missionStatement: initiative.missionStatement,
      geoLocation: initiative.geoLocation,
      industry: getIndustryText(initiative.industry),
    };
  }

  public createUTRBuilder(utrCode: string, slideId?: number): PPTXUTRBuilder {
    const utrsInputMap = this.getUtrsInputMapBySlideId(slideId);
    return new PPTXUTRBuilder(this.logger, this.repositoryManager, utrCode, utrsInputMap);
  }

  public createUTRTableBuilder(utrCode: string, slideId?: number): PPTXTableBuilder {
    const utrsInputMap = this.getUtrsInputMapBySlideId(slideId);
    return new PPTXTableBuilder(this.repositoryManager, utrCode, utrsInputMap);
  }

  public createAIBuilder(): PPTXAIBuilder {
    return new PPTXAIBuilder(this.aiService, this.getSurveyCache(), this.debugMode);
  }

  public async getUTRValueChange(
    utrCode: string,
    options: { reportOffsetFrom?: number; reportOffsetTo?: number; slideId?: number } = {}
  ) {
    const { reportOffsetFrom = ReportOffset.Previous, reportOffsetTo = ReportOffset.Current, slideId } = options;
    const valueA = await this.createUTRBuilder(utrCode, slideId).periodOffset(reportOffsetFrom).getAsNumber();
    const valueB = await this.createUTRBuilder(utrCode, slideId).periodOffset(reportOffsetTo).getAsNumber();
    return this.getChangeText(valueA, valueB);
  }

  public getChangeText(valueA: number | string | undefined | null, valueB: number | undefined | string | null): string {
    if (!isNumericString(valueA) || !isNumericString(valueB)) {
      return '-';
    }

    const from = Number(valueA);
    const to = Number(valueB);

    const pcChange = Math.round((10 * 100 * (to - from)) / from) / 10;
    if (!isFinite(pcChange)) {
      return '-';
    }
    if (pcChange > 0) {
      const change = pcChange.toFixed(1);
      return `Increase by ${change}%`;
    }
    if (pcChange < 0) {
      return `Decrease by ${Math.abs(pcChange).toFixed(1)}%`;
    }
    return 'No change';
  }

  public getPercentageText(valueA: number | null | undefined, valueB: number | null | undefined): string | number {
    const value = valueA ?? 0;
    const total = valueB ?? 0;

    if (total === 0) {
      return '-';
    }
    const percentage = Math.round((value / total) * 100);
    return percentage;
  }

  public async getScorecardScore(reportOffset: number = 0): Promise<null | number> {
    const scorecard = await this.getSurveyCache(reportOffset)?.getScorecard();
    if (scorecard?.actual === undefined) {
      return null;
    }
    return scorecard.actual;
  }

  public async getSDGByMaterialityOrder(order: number, reportOffset: number = 0) {
    if (order < 1 || order > 17) {
      return null;
    }
    const scorecard = await this.getSurveyCache(reportOffset)?.getScorecard();
    return scorecard?.goals.sort((a, b) =>
      (a.actual ?? 0) > (b.actual ?? 0) ? -1 : (a.actual ?? 0) < (b.actual ?? 0) ? 1 : 0
    )[order - 1];
  }

  public noInformation() {
    return 'No information provided';
  }

  async getLoremIpsum(words = 200) {
    const text =
      `Lorem ipsum dolor sit amet consectetur. Et ultricies fames ac lectus enim. Rhoncus tortor diam sed lectus ` +
      `quis aliquam tempor venenatis proin. Tempor sem duis quam vestibulum posuere vitae arcu. Faucibus id mauris dictum gravida ` +
      `tristique in donec quisque. Phasellus et consectetur nulla egestas est augue tincidunt eros id. Enim elementum feugiat lectus ` +
      `varius. Justo sit dui diam non mi quis. Lacus viverra ultrices ultricies fames risus ornare viverra ac. ` +
      `Turpis nisl eget accumsan at. Nunc nunc scelerisque est ultrices sed arcu in aenean morbi. Placerat risus congue.\n\n` +
      `Lorem ipsum dolor sit amet consectetur. Et ultricies fames ac lectus enim. Rhoncus tortor diam sed lectus ` +
      `quis aliquam tempor venenatis proin. Tempor sem duis quam vestibulum posuere vitae arcu. Faucibus id mauris dictum gravida ` +
      `tristique in donec quisque. Phasellus et consectetur nulla egestas est augue tincidunt eros id.Enim elementum feugiat lectus ` +
      `varius. Justo sit dui diam non mi quis. Lacus viverra ultrices ultricies fames risus ornare viverra ac.\n\n ` +
      `Turpis nisl eget accumsan at. Nunc nunc scelerisque est ultrices sed arcu in aenean morbi. Placerat risus congue. \n\n` +
      `Lorem ipsum dolor sit amet consectetur. Et ultricies fames ac lectus enim. Rhoncus tortor diam sed lectus ` +
      `quis aliquam tempor venenatis proin. Tempor sem duis quam vestibulum posuere vitae arcu. Faucibus id mauris dictum gravida ` +
      `tristique in donec quisque. Phasellus et consectetur nulla egestas est augue tincidunt eros id.Enim elementum feugiat lectus ` +
      `varius. Justo sit dui diam non mi quis. Lacus viverra ultrices ultricies fames risus ornare viverra ac.\n\n ` +
      `Turpis nisl eget accumsan at. Nunc nunc scelerisque est ultrices sed arcu in aenean morbi. Placerat risus congue.` +
      `Lorem ipsum dolor sit amet consectetur. Et ultricies fames ac lectus enim. Rhoncus tortor diam sed lectus ` +
      `quis aliquam tempor venenatis proin. Tempor sem duis quam vestibulum posuere vitae arcu. Faucibus id mauris dictum gravida ` +
      `tristique in donec quisque. Phasellus et consectetur nulla egestas est augue tincidunt eros id.Enim elementum feugiat lectus ` +
      `varius. Justo sit dui diam non mi quis. Lacus viverra ultrices ultricies fames risus ornare viverra ac.\n\n ` +
      `Turpis nisl eget accumsan at. Nunc nunc scelerisque est ultrices sed arcu in aenean morbi. Placerat risus congue. \n\n` +
      `Lorem ipsum dolor sit amet consectetur. Et ultricies fames ac lectus enim. Rhoncus tortor diam sed lectus ` +
      `quis aliquam tempor venenatis proin. Tempor sem duis quam vestibulum posuere vitae arcu. Faucibus id mauris dictum gravida ` +
      `tristique in donec quisque. Phasellus et consectetur nulla egestas est augue tincidunt eros id.Enim elementum feugiat lectus ` +
      `varius. Justo sit dui diam non mi quis. Lacus viverra ultrices ultricies fames risus ornare viverra ac.\n\n ` +
      `Turpis nisl eget accumsan at. Nunc nunc scelerisque est ultrices sed arcu in aenean morbi. Placerat risus congue.`;
    return text.split(' ').slice(0, words).join(' ').slice(0, -1).concat('.');
  }
}
