/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { SurveyRepository } from '../../repository/SurveyRepository';
import { ObjectId } from 'bson';
import { UtrValueType } from '../../models/public/universalTrackerType';
import { ValueData } from '../../models/public/universalTrackerValueType';
import { ActionList } from '../utr/constants';
import { wwgLogger } from '../wwgLogger';
import { MaterialityAssessmentType, MaterialityMetricWithUtrAndValueListPlain } from '../../models/materialityMetric';
import { MaterialityMetricRepository } from './MaterialityMetricRepository';
import { MaterialTopicPlain } from '../../models/materialTopics';
import { MaterialTopicRepository } from './MaterialTopicRepository';
import { MaterialityLookupCalculator } from './assessments/MaterialityLookupCalculator';
import { AIModelType } from '../ai/AIModelFactory';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { getIndustryText } from '../reporting/FrameworkMapping';
import { MaterialityAssessmentScopeCalculator, ScopeCalculatorContext } from './MaterialityAssessmentSizeCalculator';
import { MaterialityAssessmentUtrCodes } from '../../routes/validation-schemas/materiality-assessment';
import { MaterialityAssessmentScope } from './types';
import { AssessmentScoreMap } from '../ai/MaterialityAssessmentAIService';
import UniversalTracker, { UniversalTrackerBlueprintMin } from '../../models/universalTracker';
import { utrForBlueprintProject } from '../../repository/projections';
import { AssessmentData, AssessmentInputs, AssessmentMinData, AssessmentResult, AssessmentResultType } from './types';
import { roundTo } from '../../util/number';
import { MaterialityAssessmentContext } from '../../service/materiality-assessment/background-job/types';
import { SurveyModelPlain } from '../../models/survey';
import { mapToDoubleMaterialityData } from '../../util/materiality-assessment';
import { isDoubleMaterialitySurvey } from '../../util/survey';
import { FINANCIAL_ASSESSMENT_WEIGHTS, IMPACT_ASSESSMENT_WEIGHTS } from './constants';
import { minUtrProjection, type MinUtrLookup } from '../../types/universalTracker';

type CompanyInfo = { name: string, industry?: string, country?: string };

export class MaterialityAssessmentService {
  private inputs: AssessmentInputs | undefined = undefined;
  private companyInfo: undefined | CompanyInfo = undefined;

  private financialMaterialityMetrics: undefined | MaterialityMetricWithUtrAndValueListPlain<ObjectId>[] = undefined;
  private impactMaterialityMetrics: undefined | MaterialityMetricWithUtrAndValueListPlain<ObjectId>[] = undefined;
  private materialTopics:
  | undefined
  | Pick<
      MaterialTopicPlain,
      'code' | 'name' | 'utrMapping' | 'categories' | 'scopeScores' | 'description' | 'action'
    >[] = undefined;

  constructor(private assessmentId: ObjectId) {}

  public async getInputs() {
    if (this.inputs === undefined) {
      return this.load();
    }
    return this.inputs;
  }

  private async load() {
    if (this.inputs) {
      return this.inputs;
    }

    const [survey] = await SurveyRepository.getInitiativeSurveyUtrvActions(this.assessmentId);
    const inputs: AssessmentInputs = new Map<string, string[]>();

    survey.fragmentUniversalTrackerValues
      ?.filter((utrv) => utrv.status === ActionList.Verified)
      .forEach((utrv) => {
        const utr = survey.fragmentUniversalTracker?.find((u) => u._id.equals(utrv.universalTrackerId));
        if (!utr) {
          wwgLogger.error('No UTR found for a fragment in MaterialityAssessmentService', {
            surveyId: String(survey._id),
            initiativeId: String(survey.initiativeId),
            utrvId: String(utrv._id),
            utrId: String(utrv.universalTrackerId),
          });
          return;
        }

        const data = (utrv.valueData as ValueData<string | string[]>)?.data;

        if (!data) {
          return;
        }
        switch (utr.valueType) {
          case UtrValueType.Text:
          case UtrValueType.ValueList:
          case UtrValueType.ValueListMulti:
          case UtrValueType.NumericValueList:
            inputs.set(utr.code, Array.isArray(data) ? data : [data]);
            break;
        }
      });
      this.inputs = inputs;

      const initiative = await InitiativeRepository.mustFindById(survey.initiativeId, { name: 1, industry: 1, country: 1 });
      this.companyInfo = {
        name: initiative.name,
        industry: getIndustryText(initiative.industry),
        country: initiative.country
      };
      return this.inputs;
  }

  private async loadMaterialityMetrics(inputs: AssessmentInputs) {
    const utrCodes = Array.from(inputs.keys());
    this.financialMaterialityMetrics = await MaterialityMetricRepository.findFinancialByUtrCodes(utrCodes);
    this.impactMaterialityMetrics = await MaterialityMetricRepository.findImpactByUtrCodes(utrCodes);
  }

  private async loadMaterialityTopics(topicCodes: string[]) {
    this.materialTopics = await MaterialTopicRepository.findByCodes(topicCodes);
  }

  private getMaterialityTopic(topicCode: string) {
    return this.materialTopics?.find((topic) => topic.code === topicCode);
  }

  private async getLookupResult(
    scores: AssessmentScoreMap,
    scope: MaterialityAssessmentScope,
    type: MaterialityAssessmentType
  ) {

    if (!this.financialMaterialityMetrics) {
      return [];
    }

    const maxScore = await MaterialTopicRepository.findMaxScore(scope, type);
    // Implement =100*(LN(A2+1)/LN($C$1+1))^2
    const calculateRelativeScore = (score: number, maxScore: number) => {
      if (!maxScore) {
        return undefined; // Keep logic as original for fallback
      }
      return roundTo(100 * Math.pow(Math.log(score + 1) / Math.log(maxScore + 1), 2), 2);
    };

    return Object.entries(scores).map(([code, value]) => {
      return {
        code,
        score: roundTo(value, 2),
        relativeScore: calculateRelativeScore(value, maxScore),
      };
    });
  }

  // private async getSheydaLookupResult(scores: AssessmentScoreMap) {
  //   if (!this.financialMaterialityMetrics) {
  //     return [];
  //   }

  //   // Convert to array and sort by score in descending order
  //   const sortedScores = Object.entries(scores)
  //     .map(([code, value]) => ({ code, score: value }))
  //     .sort((a, b) => b.score - a.score);

  //   // Find the maximum score from the sorted scores
  //   const maxScore = sortedScores.length > 0 ? (sortedScores[0].score / 1000 ) : 0;

  //   return sortedScores.map(({ code, score }, index) => {
  //     // Apply Excel formula: IF(B3 <= 10, B3 + (10 - MAX(B:B)) * 0.99, 10)
  //     const nextScore = (sortedScores[index + 1]?.score ?? 0) / 1000;
  //     const relativeScore = nextScore <= 10
  //       ? nextScore + (10 - maxScore) * 0.99
  //       : 10;

  //     return {
  //       code,
  //       score: roundTo(score, 2),
  //       relativeScore: roundTo(relativeScore*10, 2),
  //     };
  //   });
  // }
  public async getResult(
    { modelType = AIModelType.ChatGPT, debugMode = false  }: { modelType?: AIModelType, debugMode?: Boolean } = {}
  ): Promise<AssessmentResult> {

    const inputs = await this.getInputs();
    await this.loadMaterialityMetrics(inputs);

    if (!this.financialMaterialityMetrics || !this.companyInfo) {
      return { financial: [], nonFinancial: [] };
    }

    const context: ScopeCalculatorContext = {
      [MaterialityAssessmentUtrCodes.OperationTime]: inputs.get(MaterialityAssessmentUtrCodes.OperationTime)?.[0] ?? 'blue',
      [MaterialityAssessmentUtrCodes.NumStaff]: inputs.get(MaterialityAssessmentUtrCodes.NumStaff)?.[0] ?? 'blue',
      [MaterialityAssessmentUtrCodes.AnnualSales]: inputs.get(MaterialityAssessmentUtrCodes.AnnualSales)?.[0] ?? 'blue',
      [MaterialityAssessmentUtrCodes.CapitalEmployed]: inputs.get(MaterialityAssessmentUtrCodes.CapitalEmployed)?.[0] ?? 'blue',
    }

    const scope = MaterialityAssessmentScopeCalculator.getSizeScopeTag(context);

    const financialCalculator = new MaterialityLookupCalculator(
      inputs,
      this.financialMaterialityMetrics,
      FINANCIAL_ASSESSMENT_WEIGHTS
    );
    const financialResult = await financialCalculator.getResult(debugMode);
    const financialResults = await this.getLookupResult(
      financialResult.scores,
      scope,
      MaterialityAssessmentType.Financial
    );

    const impactCalculator = new MaterialityLookupCalculator(
      inputs,
      this.impactMaterialityMetrics ?? [],
      IMPACT_ASSESSMENT_WEIGHTS
    );
    const impactResult = await impactCalculator.getResult(debugMode);
    const impactResults = await this.getLookupResult(impactResult.scores, scope, MaterialityAssessmentType.Impact);

    return {
      financial: financialResults,
      nonFinancial: impactResults,
      debug: debugMode ? {
        [AssessmentResultType.Financial]: financialCalculator.getDebugData(),
        [AssessmentResultType.Impact]: impactCalculator.getDebugData()
      } : undefined,
    };
  }

  public async hydrateTopics(data: AssessmentResult<AssessmentMinData>): Promise<AssessmentResult<AssessmentData>> {
    const topicCodes = new Set([
      ...data.financial.map((d) => d.code),
      ...data.nonFinancial.map((d) => d.code),
    ]);
    await this.loadMaterialityTopics(Array.from(topicCodes));

    return {
      financial: this.hydrateTopicData(data.financial),
      nonFinancial: this.hydrateTopicData(data.nonFinancial),
      nonFinancialReferences: data.nonFinancialReferences,
    };
  }

  private hydrateTopicData(scores: AssessmentMinData[]): AssessmentData[] {
    return scores.map((score) => {
      const materialityTopic = this.getMaterialityTopic(score.code);
      return {
        ...score,
        name: materialityTopic?.name,
        utrMapping: materialityTopic?.utrMapping,
        categories: materialityTopic?.categories,
        description: materialityTopic?.description,
        action: materialityTopic?.action,
      };
    });
  }

  public async hydrateFinancialTopicData(scores: AssessmentMinData[]): Promise<AssessmentData[]> {
    const codesToLoad = scores.map((s) => s.code);
    await this.loadMaterialityTopics(Array.from(codesToLoad));
    return this.hydrateTopicData(scores);
  }

  public async getAssessmentMappedUtrs(
    result: AssessmentResult<AssessmentMinData>
  ): Promise<UniversalTrackerBlueprintMin[]> {
    const { financial, nonFinancial } = await this.hydrateTopics(result);

    const utrCodes = new Set(
      [...financial, ...nonFinancial].flatMap((data) => data.utrMapping || []).map((d) => d.code)
    );

    return UniversalTracker.find({ code: { $in: Array.from(utrCodes) } }, utrForBlueprintProject)
      .lean()
      .exec();
  }

  public getOrderedFinancialAssessmentData(data: MaterialityAssessmentContext | undefined) {
    if (!data) {
      return undefined;
    }
    if (!data.config) {
      return data.result?.[AssessmentResultType.Financial].toSorted((a, b) => b.score - a.score);
    }
    const topicsMap = new Map(
      (data.result?.[AssessmentResultType.Financial] ?? []).map((topic) => [topic.code, topic])
    );
    return data.config.orderedTopics.reduce((topics, { code, disabled }) => {
      const topic = topicsMap.get(code);
      // Filter out hidden topics
      if (topic && !disabled) {
        topics.push(topic);
      }
      return topics;
    }, [] as AssessmentData[]);
  }

  private getOrderedDoubleAssessmentData(data: MaterialityAssessmentContext | undefined) {
    const orderedFinancialAssessmentData = this.getOrderedFinancialAssessmentData(data);
    if (!orderedFinancialAssessmentData) {
      return;
    }
    return mapToDoubleMaterialityData({
      financialData: orderedFinancialAssessmentData,
      nonFinancialData: data?.result?.[AssessmentResultType.Impact] ?? [],
      hasCustomOrder: !!data?.config?.orderedTopics.length,
    })
  }

  public async getLimitedTopicsWithUtrMappings({
    data,
    topTopicsCount,
    survey,
  }: {
    survey: Pick<SurveyModelPlain, 'assessmentType' | 'type'>;
    data: MaterialityAssessmentContext;
    topTopicsCount?: number;
  }) {
    const orderedTopics = isDoubleMaterialitySurvey(survey)
      ? this.getOrderedDoubleAssessmentData(data)
      : this.getOrderedFinancialAssessmentData(data);

    if (!orderedTopics) {
      return { utrs: [], limitedTopics: [] };
    }

    const limitedTopics = topTopicsCount ? orderedTopics.slice(0, topTopicsCount) : orderedTopics;

    await this.loadMaterialityTopics(limitedTopics.map((s) => s.code));

    const utrCodes = new Set(
      this.hydrateTopicData(limitedTopics)
        .flatMap((data) => data.utrMapping || [])
        .map((d) => d.code)
    );

    const utrs: MinUtrLookup[] = await UniversalTracker.find({ code: { $in: Array.from(utrCodes) } }, minUtrProjection)
      .lean<MinUtrLookup[]>()
      .exec();

    return { utrs, limitedTopics: this.materialTopics ?? [] };  
  }
}
