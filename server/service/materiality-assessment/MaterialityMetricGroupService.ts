import { type ObjectId } from 'bson';
import config from '../../config';
import ContextError from '../../error/ContextError';
import MetricGroup, {
  CustomMetricOrderType,
  type MetricGroupModel,
  MetricGroupSourceType,
  MetricGroupType,
  type MetricGroupPlain,
} from '../../models/metricGroup';
import Survey, { type SurveyModelPlain } from '../../models/survey';
import { type SupportedJobModel } from './background-job/types';
import { getMaterialityAssessmentBackgroundJobService } from './MaterialityAssessmentBackgroundJobService';
import { getMaterialityAssessmentManager } from './MaterialityAssessmentManager';
import { customDateFormat, DateFormat } from '../../util/date';
import { MaterialityAssessmentService } from './MaterialityAssessmentService';
import { type InitiativePlain } from '../../models/initiative';
import { excludeSoftDeleted } from '../../repository/aggregations';
import { type KeysEnum } from '../../models/public/projectionUtils';
import { isMaterialitySurvey, SurveyType } from '../../util/survey';
import { MaterialPillar, type MaterialTopicPlain } from '../../models/materialTopics';
import { capitalize } from '../../util/string';
import { metricGroupAssessmentTypeLabelMap } from './constants';
import { AssessmentType } from '../../types/materiality-assessment';
import { type UniversalTrackerPlain } from '../../models/universalTracker';
import { MetricGroupRepository } from '../../repository/MetricGroupRepository';

const DEFAULT_GROUP_COLOR = '#9BCDEF';

interface GenerateUtrsParams {
  metricGroup: MetricGroupModel;
  job: SupportedJobModel;
  topTopicsCountOverride?: number;
  survey: Pick<SurveyModelPlain, '_id' | 'type' | 'assessmentType' | 'completedDate' | 'effectiveDate'>;
}

type SurveyMin = Pick<
  SurveyModelPlain,
  '_id' | 'initiativeId' | 'effectiveDate' | 'type' | 'assessmentType' | 'completedDate'
>;

const surveyProjection: KeysEnum<SurveyMin> = {
  _id: 1,
  type: 1,
  initiativeId: 1,
  effectiveDate: 1,
  completedDate: 1,
  assessmentType: 1,
};

type SubgroupMin = Pick<MetricGroupPlain, '_id'>;

const subgroupProjection: KeysEnum<SubgroupMin> = {
  _id: 1,
};

export class MaterialityMetricGroupService {
  constructor(
    private surveyModel: typeof Survey,
    private materialityAssessmentManager: ReturnType<typeof getMaterialityAssessmentManager>,
    private backgroundJobMTService: ReturnType<typeof getMaterialityAssessmentBackgroundJobService>,
    private metricGroupRepository: typeof MetricGroupRepository
  ) {}

  public async findOrCreateMetricGroup({ userId, survey }: { userId: ObjectId; survey: SurveyMin }) {
    const { _id: surveyId, initiativeId } = survey;

    const match = {
      initiativeId,
      'source.type': MetricGroupSourceType.Survey,
      'source.surveyId': surveyId,
      parentId: { $exists: false },
    };

    const metricGroup = await MetricGroup.findOne(match).populate('survey').exec();

    if (metricGroup) {
      return metricGroup;
    }

    return new MetricGroup({
      initiativeId: initiativeId,
      type: MetricGroupType.Custom,
      groupName: this.generateGroupName(survey),
      groupData: { icon: `${config.assets.logoSrcRoot}/materiality_tracker_logo.svg`, colour: DEFAULT_GROUP_COLOR },
      metricsOrder: { orderType: CustomMetricOrderType.TypeCode },
      createdBy: userId,
      source: { type: MetricGroupSourceType.Survey, surveyId },
    });
  }

  public async generateMetricGroupUtrs({ userId, job }: { userId: ObjectId } & Pick<GenerateUtrsParams, 'job'>) {
    const { surveyId } = job.tasks[0].data;
    const survey = await this.surveyModel
      .findOne({ _id: surveyId, ...excludeSoftDeleted() }, surveyProjection)
      .orFail()
      .lean()
      .exec();
    if (!isMaterialitySurvey(survey)) {
      throw new ContextError('Survey is not a materiality survey', { surveyId, jobId: job._id });
    }

    const metricGroup = await this.findOrCreateMetricGroup({ userId, survey });

    if (metricGroup.source?.jobId) {
      return metricGroup;
    }

    return this.processGenerateUtrs({ metricGroup, job, survey });
  }

  public async regenerateMetricGroupUtrs({ groupId, topTopicsCount }: { groupId: ObjectId; topTopicsCount: number }) {
    const metricGroup = await MetricGroup.findById(groupId).populate('survey').orFail().exec();

    const { surveyId } = metricGroup.source ?? {};

    if (!surveyId || !metricGroup.survey) {
      throw new ContextError('No survey found for metric group', { metricGroupId: groupId });
    }

    // find the latest score job
    const job = await this.backgroundJobMTService.findExistingJob({
      initiativeId: metricGroup.initiativeId,
      surveyId: surveyId,
    });

    if (!job) {
      throw new ContextError('No score job found for metric group', { metricGroupId: groupId });
    }

    const subGroupsCount = await MetricGroup.countDocuments({ parentId: metricGroup._id });

    const currentTopTopicsCount = metricGroup.source?.topTopicsCount;
    const shouldUpdate =
      !metricGroup.source?.jobId ||
      job.updated > metricGroup.updated ||
      currentTopTopicsCount !== topTopicsCount ||
      subGroupsCount === 0; // Legacy MT metric groups were not generated with  pillar-based subgroups — flag for update

    if (!shouldUpdate) {
      return metricGroup;
    }

    return this.processGenerateUtrs({
      metricGroup,
      job,
      survey: metricGroup.survey,
      topTopicsCountOverride: topTopicsCount,
    });
  }

  public async processGenerateUtrs({ metricGroup, job, survey, topTopicsCountOverride }: GenerateUtrsParams) {
    if (!survey.completedDate) {
      throw new ContextError('Survey is not completed', { surveyId: survey._id });
    }
    if (!metricGroup.source) {
      throw new ContextError('No source found for metric group', { metricGroupId: metricGroup._id });
    }
    const topTopicsCount = await this.materialityAssessmentManager.getTopTopicsCount(
      survey._id,
      topTopicsCountOverride ?? metricGroup.source.topTopicsCount
    );
    const materialityTopicService = new MaterialityAssessmentService(survey._id);
    const { limitedTopics, utrs } = await materialityTopicService.getLimitedTopicsWithUtrMappings({
      data: job.tasks[0].data,
      topTopicsCount,
      survey,
    });

    const topicUtrs = utrs.map(({ _id }) => ({ _id }));

    metricGroup.source.topTopicsCount = topTopicsCount;
    metricGroup.source.topicUtrs = topicUtrs;
    metricGroup.source.jobId = job._id;
    // all modifications to the existing groups will be discarded
    metricGroup.universalTrackers = topicUtrs.map(({ _id }) => _id);
    metricGroup.updated = new Date();

    // update to have format: Month Year (Assessment type)
    metricGroup.groupName = this.generateGroupName(survey);
    metricGroup.groupData.colour = DEFAULT_GROUP_COLOR;

    await this.updateOrCreateSubgroups({
      metricGroup,
      utrsPerPillarMap: this.createUtrsPerPillarMap(limitedTopics, utrs),
    });

    return metricGroup.save();
  }

  private createUtrsPerPillarMap(
    limitedTopics: Pick<MaterialTopicPlain, 'categories' | 'utrMapping'>[],
    utrs: Pick<UniversalTrackerPlain, '_id' | 'code'>[]
  ) {
    return limitedTopics.reduce<Record<string, Set<string>>>((acc, topic) => {
      if (!topic.categories?.materialPillar) {
        return acc;
      }
      topic.categories.materialPillar.forEach((pillar) => {
        const utrCodes = topic.utrMapping?.map((m) => m.code) ?? [];
        const utrIds = utrs.filter((utr) => utrCodes.includes(utr.code)).map((utr) => utr._id.toString());
        acc[pillar] = new Set([...acc[pillar], ...utrIds]);
      });
      return acc;
    }, Object.fromEntries(Object.values(MaterialPillar).map((pillar) => [pillar, new Set()])));
  }

  private generateGroupName(survey: Pick<SurveyMin, 'effectiveDate' | 'assessmentType'>) {
    const { effectiveDate, assessmentType = AssessmentType.FinancialMateriality } = survey;
    return `${customDateFormat(effectiveDate, DateFormat.MonthYear)} (${
      metricGroupAssessmentTypeLabelMap[assessmentType]
    })`;
  }

  private async updateOrCreateSubgroups({
    metricGroup,
    utrsPerPillarMap,
  }: {
    metricGroup: MetricGroupModel;
    utrsPerPillarMap: Record<string, Set<string>>;
  }) {
    const existingSubgroups = await MetricGroup.find({ parentId: metricGroup._id }).lean().exec();

    const subgroupsToAdd = [];

    const subgroupsToUpdate = [];

    for (const [pillar, utrIds] of Object.entries(utrsPerPillarMap)) {
      const existingSubgroup = existingSubgroups.find((g) => g.groupData.code === pillar);

      if (existingSubgroup) {
        subgroupsToUpdate.push({
          updateOne: {
            filter: { _id: existingSubgroup._id },
            update: {
              universalTrackers: Array.from(utrIds),
              updated: new Date(),
              source: {
                ...existingSubgroup.source,
                jobId: metricGroup.source?.jobId,
                topTopicsCount: metricGroup.source?.topTopicsCount,
                topicUtrs: Array.from(utrIds).map((_id) => ({ _id })),
              },
            },
          },
        });
        continue;
      }

      subgroupsToAdd.push({
        insertOne: {
          document: {
            initiativeId: metricGroup.initiativeId,
            groupName: `Pillar: ${capitalize(pillar)}`,
            groupData: { icon: `${config.assets.cdn}/images/${pillar}.png`, code: pillar },
            universalTrackers: Array.from(utrIds),
            metricsOrder: { orderType: CustomMetricOrderType.TypeCode },
            type: MetricGroupType.Custom,
            parentId: metricGroup._id,
            createdBy: metricGroup.createdBy,
            updated: new Date(),
            source: {
              ...metricGroup.toObject().source,
              topicUtrs: Array.from(utrIds).map((_id) => ({ _id })),
            },
          },
        },
      });
    }

    await MetricGroup.bulkWrite([...subgroupsToAdd, ...subgroupsToUpdate]);
  }

  public async getLatestMetricGroup(initiative: Pick<InitiativePlain, '_id'>, userId: ObjectId) {
    const initiativeId = initiative._id;
    const survey = await this.surveyModel
      .findOne(
        {
          initiativeId,
          type: SurveyType.Materiality,
          completedDate: { $exists: true },
          ...excludeSoftDeleted(),
        },
        surveyProjection
      )
      .sort({ effectiveDate: -1 })
      .lean()
      .exec();

    if (!survey) {
      return;
    }

    const metricGroup = await this.findOrCreateMetricGroup({ userId, survey });

    if (metricGroup.source?.jobId) {

      return {
        metricGroupId: metricGroup._id,
        subGroupIds: await this.getAllSubgroupIds(metricGroup._id),
        effectiveDate: survey.effectiveDate,
      };
    }

    // If jobId is undefined (latest score job deleted or metric group's UTRS is not generated yet (legacy completed assessments))
    // generate the UTRS using another score job to update the metric group.
    const job = await this.backgroundJobMTService.findExistingJob({ initiativeId, surveyId: survey._id });

    if (job) {
      await this.processGenerateUtrs({ metricGroup, job, survey });
      return {
        metricGroupId: metricGroup._id,
        subGroupIds: await this.getAllSubgroupIds(metricGroup._id),
        effectiveDate: survey.effectiveDate,
      };
    }
  }

  private async getAllSubgroupIds(metricGroupId: ObjectId): Promise<ObjectId[]> {
    const subgroups = await this.metricGroupRepository.getAllChildrenById(metricGroupId, subgroupProjection);
    return subgroups.map(({ _id }) => _id);
  }
}

let instance: MaterialityMetricGroupService;

export const getMaterialityMetricGroupService = () => {
  if (!instance) {
    instance = new MaterialityMetricGroupService(
      Survey,
      getMaterialityAssessmentManager(),
      getMaterialityAssessmentBackgroundJobService(),
      MetricGroupRepository
    );
  }
  return instance;
};
