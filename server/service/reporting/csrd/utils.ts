import { UtrValueType } from '../../../models/public/universalTrackerType';
import { $createLineBreakNode, $createParagraphNode, $createTextNode } from 'lexical';
import { $createIxbrlNode, type IXBRLNode } from '../lexical/nodes/IXBRLNode';
import { CSRDDefinitions } from './CsrdDefinitions';
import type { SectionData } from '../xhtml/types';
import { getDefaultESRSMappingList } from './CsrdMappingList';
import { numberScaleValueMap, NumberScale } from '../../units/unitTypes';
import type { CSRDFact, CSRDMappingItem, UtrvData, XBRLMapping } from '../types';

export const MAJOR_SECTION = {
  BP: {
    code: 'BP',
    description: 'Basis for Preparation',
  },
  SBM: {
    code: 'SBM',
    description: 'Strategy, Business Model, and Materiality',
  },
  E1: {
    code: 'E1',
    description: 'Climate Change',
  },
  E2: {
    code: 'E2',
    description: 'Pollution',
  },
  E3: {
    code: 'E3',
    description: 'Water and Marine Resources',
  },
  E4: {
    code: 'E4',
    description: 'Biodiversity and Ecosystems',
  },
  E5: {
    code: 'E5',
    description: 'Resource Use and Circular Economy',
  },
  S1: {
    code: 'S1',
    description: 'Own Workforce',
  },
  S2: {
    code: 'S2',
    description: 'Workers in the Value Chain',
  },
  S3: {
    code: 'S3',
    description: 'Affected Communities',
  },
  S4: {
    code: 'S4',
    description: 'Consumers and End-users',
  },
  G1: {
    code: 'G1',
    description: 'Business Conduct',
  },
};

export const getCsrdMapping = (overrides: XBRLMapping<CSRDMappingItem> = {}): XBRLMapping<CSRDMappingItem> => {
  return getDefaultESRSMappingList().reduce((acc, item) => {
    if (!acc[item.factName]) {
      acc[item.factName] = {
        factName: item.factName,
        utrCode: item.utrCode,
        valueListCode: item.valueListCode,
      };
    }
    return acc;
  }, overrides);
};

type GetDataParams = {
  factName: string;
  mapping: XBRLMapping;
  utrCodeToUtrvMap: Map<string, UtrvData>;
  fallback?: string;
};

export function getData({ factName, mapping, utrCodeToUtrvMap, fallback = '' }: GetDataParams): string | number {
  const item = mapping[factName];
  if (!item) return fallback;
  const utrv = utrCodeToUtrvMap.get(item.utrCode);
  if (!utrv) return fallback;

  switch (utrv.universalTracker.valueType) {
    case UtrValueType.Number:
    case UtrValueType.Sample:
    case UtrValueType.Percentage:
      return utrv.value ?? fallback;
    case UtrValueType.Text:
    case UtrValueType.Date:
      return utrv.valueData?.data ?? fallback;
    case UtrValueType.Table: {
      if (!item.valueListCode) {
        return fallback;
      }
      const firstRow = utrv.valueData?.table?.[0];
      const value = firstRow?.find((col) => col.code === item.valueListCode)?.value;
      return Array.isArray(value) ? value.join(', ') : value ?? fallback;
    }
    case UtrValueType.NumericValueList:
    case UtrValueType.TextValueList:
      if (!item.valueListCode) {
        return fallback;
      }
      return utrv.valueData?.data?.[item.valueListCode] ?? fallback;
    default:
      return fallback;
  }
}

export const getStringData = (params: GetDataParams) => {
  return String(
    getData({
      ...params,
      fallback: params.fallback ?? '-',
    })
  );
};

export const getFactLabel = (tag: string): string => {
  const def = CSRDDefinitions[tag];
  return def?.label ?? '';
};

export const getFactSectionAndDataPoint = (tag: string) => {
  const def = CSRDDefinitions[tag];
  if (!def?.references) {
    return;
  }

  return def.references.reduce(
    (acc, ref) => {
      const sectionEntry = ref.find(([key]) => key === 'Section');
      const dataPointEntry = ref.find(([key]) => key === 'DatapointId');
      if (sectionEntry) {
        acc.section = sectionEntry[1];
      }
      if (dataPointEntry) {
        acc.dataPointId = dataPointEntry[1];
      }
      return acc;
    },
    { section: undefined as string | undefined, dataPointId: undefined as string | undefined }
  );
};

export const getMajorSection = (sectionCode: string | undefined): string | undefined => {
  if (!sectionCode) {
    return;
  }

  const match = sectionCode.match(/^(BP|SBM|E1|E2|E3|E4|E5|S1|S2|S3|S4|G1)(-|$)/);
  return match ? match[1] : undefined;
};

/**
 * Returns an array of ESRSMappingItem from the mapping whose section matches the given majorSection,
 * sorted by section and dataPointId order.
 */
export const getOrderedMappingItems = ({
  mapping,
  majorSection,
}: {
  mapping: XBRLMapping<CSRDMappingItem>;
  majorSection: string;
}): CSRDMappingItem[] => {
  // Collect items with their section/dataPointId
  const itemsWithTags = Object.values(mapping)
    .filter((item): item is CSRDMappingItem => !!item)
    .map((item) => {
      const tag = getFactSectionAndDataPoint(item.factName);
      return { item, tag };
    })
    .filter(({ tag }) => {
      const major = getMajorSection(tag?.section);
      return major === majorSection;
    });

  // Sort using the same logic as sortTagsBySectionAndDataPoint
  itemsWithTags.sort((a, b) => {
    const aMajor = getMajorSection(a.tag?.section);
    const bMajor = getMajorSection(b.tag?.section);

    if (aMajor === undefined && bMajor !== undefined) return 1;
    if (aMajor !== undefined && bMajor === undefined) return -1;
    if (aMajor !== undefined && bMajor !== undefined) {
      if (aMajor < bMajor) return -1;
      if (aMajor > bMajor) return 1;
    }

    if (a.tag?.dataPointId === undefined && b.tag?.dataPointId !== undefined) return 1;
    if (a.tag?.dataPointId !== undefined && b.tag?.dataPointId === undefined) return -1;
    if (a.tag?.dataPointId !== undefined && b.tag?.dataPointId !== undefined) {
      if (a.tag.dataPointId < b.tag.dataPointId) return -1;
      if (a.tag.dataPointId > b.tag.dataPointId) return 1;
    }

    return 0;
  });

  return itemsWithTags.map(({ item }) => item);
};

type BuildIxbrlNodesFromMappingParams = Pick<
  SectionData<CSRDMappingItem>,
  'mapping' | 'utrCodeToUtrvMap' | 'tracker'
> & {
  majorSection: string;
};

/**
 * Build an array of ixbrlNodes for a given majorSection from mapping, utrvData, and tracker.
 */
export const buildIxbrlNodesFromMapping = ({
  mapping,
  utrCodeToUtrvMap,
  tracker,
  majorSection,
}: BuildIxbrlNodesFromMappingParams): { factName: CSRDFact; ixbrlNode: IXBRLNode }[] => {
  const orderedItems = getOrderedMappingItems({ mapping, majorSection });

  return orderedItems.map((item) => {
    const utrv = utrCodeToUtrvMap.get(item.utrCode);

    const value = getData({ factName: item.factName, mapping, utrCodeToUtrvMap, fallback: '-' });
    let tag: 'ix:nonFraction' | 'ix:nonNumeric' = 'ix:nonFraction';
    let format: string | undefined = undefined;
    let decimals: number | undefined = undefined;
    let scale: number | undefined = undefined;
    let unitRef: string | undefined = undefined;

    if (utrv) {
      const valueType = utrv.universalTracker.valueType;
      if (
        valueType === UtrValueType.Number ||
        valueType === UtrValueType.Percentage ||
        valueType === UtrValueType.NumericValueList
      ) {
        format = 'ixt4:num-dot-decimal';
        decimals = 2;
        scale = numberScaleValueMap[utrv.universalTracker.numberScale ?? NumberScale.Single];
        unitRef = utrv.universalTracker.unit ? tracker.addUnitRef(utrv.universalTracker.unit) : undefined;
      } else {
        tag = 'ix:nonNumeric';
        format = undefined;
        scale = undefined;
        unitRef = undefined;
      }
    }

    const ixbrlNode = $createIxbrlNode({
      tag,
      name: item.factName,
      format,
      decimals,
      scale,
      unitRef: unitRef ?? '',
      contextRef: tracker.getContextId(),
      factId: tracker.getFactId(),
    });
    ixbrlNode.append($createTextNode(String(value)));
    return { factName: item.factName, ixbrlNode };
  });
};

export const getContextualIxbrlNodes = (params: BuildIxbrlNodesFromMappingParams) => {
  const builtNodes = buildIxbrlNodesFromMapping(params);

  return builtNodes.map((node) => {
    const paragraph = $createParagraphNode();
    paragraph.append(
      $createTextNode(`${getFactLabel(node.factName)}`),
      $createLineBreakNode(),
      $createTextNode('Value:  '),
      node.ixbrlNode
    );
    return paragraph;
  });
};
