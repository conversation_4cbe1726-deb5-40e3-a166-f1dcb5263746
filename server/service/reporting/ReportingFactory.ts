/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import ContextError from '../../error/ContextError';
import { ReportDocumentType } from '../../models/reportDocument';
import { getCsrdLexicalStateGenerator } from './csrd/CsrdLexicalStateGenerator';
import { CSRDDefinitions } from './csrd/CsrdDefinitions';
import type { CSRDMappingItem, ILexicalStateGenerator, ISSBMappingItem, ReportDefinition, XBRLMapping } from './types';
import { getCsrdMapping } from './csrd/utils';

const ISSB_MESSAGE = 'ISSB report type not yet implemented.';

export class ReportingFactory {
  public getLexicalStateGenerator(reportType: ReportDocumentType): ILexicalStateGenerator {
    switch (reportType) {
      case ReportDocumentType.CSRD:
        return getCsrdLexicalStateGenerator();
      case ReportDocumentType.ISSB:
        throw new ContextError(ISSB_MESSAGE);
      default:
        throw new ContextError(`Unknown report type: ${reportType}`);
    }
  }

  public getReportDefinition(reportType: ReportDocumentType): ReportDefinition {
    switch (reportType) {
      case ReportDocumentType.CSRD:
        return CSRDDefinitions;
      case ReportDocumentType.ISSB:
        throw new ContextError(ISSB_MESSAGE);
      default:
        throw new ContextError(`Unknown report type: ${reportType}`);
    }
  }

  public getReportMapping({
    reportType,
    externalMapping,
  }:
    | {
        reportType: ReportDocumentType.CSRD;
        externalMapping: XBRLMapping<CSRDMappingItem>;
      }
    | {
        reportType: ReportDocumentType.ISSB;
        externalMapping: XBRLMapping<ISSBMappingItem>;
      }) {
    switch (reportType) {
      case ReportDocumentType.CSRD:
        return getCsrdMapping(externalMapping);
      case ReportDocumentType.ISSB:
        throw new ContextError(ISSB_MESSAGE);
      default:
        throw new ContextError(`Unknown report type: ${reportType}`);
    }
  }
}

let instance: ReportingFactory;
export const getReportingFactory = () => {
  if (!instance) {
    instance = new ReportingFactory();
  }
  return instance;
};
