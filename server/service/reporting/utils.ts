import { type CreateEditorArgs, ParagraphNode } from 'lexical';
import { wwgLogger } from '../wwgLogger';
import { ListItemNode, ListNode } from '@lexical/list';
import { HeadingNode } from '@lexical/rich-text';
import { IXBRLNode } from './lexical/nodes/IXBRLNode';

export const editorConfig: CreateEditorArgs = {
  onError: (error: Error) => {
    wwgLogger.error(error);
  },
  theme: {
    ltr: 'ltr',
    rtl: 'rtl',
    placeholder: 'editor-placeholder',
    paragraph: 'editor-paragraph',
    list: {
      nested: {
        listitem: 'editor-nested-listitem',
      },
      ol: 'editor-list-ol',
      ul: 'editor-list-ul',
      listitem: 'editor-listitem',
    },
    text: {
      bold: 'editor-text-bold',
      italic: 'editor-text-italic',
      overflowed: 'editor-text-overflowed',
      underline: 'editor-text-underline',
    },
    ixbrlNode: 'ixbrl-plugin-node',
  },
  namespace: 'ReportEditor',
  nodes: [ListNode, ListItemNode, HeadingNode, IXBRLNode, ParagraphNode],
};
