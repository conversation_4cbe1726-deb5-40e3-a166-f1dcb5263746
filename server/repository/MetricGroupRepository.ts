/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { ObjectId } from 'bson';
import MetricGroup, { MetricGroupModel, MetricGroupPlain, MetricGroupType } from "../models/metricGroup";
import { FilterQuery, ProjectionType } from 'mongoose';
import { InitiativeRepository } from './InitiativeRepository';
import { utrForBlueprintProject } from './projections';
import { UniversalTrackerRepository } from './UniversalTrackerRepository';
import { UniversalTrackerPlain } from '../models/universalTracker';
import { KeysEnum } from "../models/public/projectionUtils";

export type MetricGroupMin = Pick<MetricGroupPlain, 'groupName' | 'groupData' | 'universalTrackers'>;

export class MetricGroupRepository {

  public static async mustFindById(id: string | ObjectId): Promise<MetricGroupModel> {

    if (!ObjectId.isValid(id)) {
      throw new Error(`MetricGroup id is not valid`);
    }

    const metricGroup = await MetricGroup.findById(id).exec();
    if (!metricGroup) {
      throw new Error(`MetricGroup was not found with id "${id}"`);
    }

    return metricGroup;
  }

  public static async getUtrIds(ids: (ObjectId | string)[]): Promise<ObjectId[]> {
    const groupIds = ids.map(id => new ObjectId(id))

    const [group] = await MetricGroup.aggregate([
      {
        $match: {
          _id: { $in: groupIds }
        }
      },
      { $unwind: "$universalTrackers" },
      {
        $group: {
          _id: null,
          universalTrackers: {
            $addToSet: "$universalTrackers"
          }
        }
      },
    ]).exec();

    return group ? group.universalTrackers : [];
  }

  public static async find(match: FilterQuery<MetricGroupPlain>, projection?: ProjectionType<MetricGroupPlain>,) {
    return MetricGroup.find(match, projection).lean().exec();
  }

  public static async getMetricGroupsByIds(ids: (ObjectId | string)[], projection?: ProjectionType<MetricGroupPlain>) {
    const groupIds = ids.map((id) => new ObjectId(id));
    return MetricGroup.find({ _id: { $in: groupIds } }, projection);
  }

  public static async getInitiativeAndDescendantsKpis(initiativeId: string | ObjectId) {
    const children = await InitiativeRepository.getAllChildrenById(initiativeId, undefined, { _id: 1 });
    const childIds = children.map((initiative) => initiative._id);
    const metricGroups = await MetricGroup.find({
      type: MetricGroupType.Custom,
      $or: [
        { initiativeId: { $in: childIds } },
        {
          'share.initiativeId': { $in: childIds },
        },
      ],
    })
      .populate('universalTracker', utrForBlueprintProject)
      .lean()
      .exec();

    const groupUtrs = metricGroups.map((g) => g.universalTracker ?? []).flat() as UniversalTrackerPlain[];

    const utrs = await UniversalTrackerRepository.find(
      { ownerId: { $in: childIds, $nin: groupUtrs.map((u) => u._id) } },
      utrForBlueprintProject,
      { lean: true }
    ) as UniversalTrackerPlain[];

    return groupUtrs.concat(utrs);
  }

  public static async getMetricGroupsByInitiativeId(params: { utrIds: ObjectId[]; initiativeId: ObjectId }) {
    const { utrIds, initiativeId } = params;

    const children = await InitiativeRepository.getAllChildrenById(initiativeId, undefined, { _id: 1 });
    const childIds = children.map((initiative) => initiative._id);

    return MetricGroup.find({
        type: MetricGroupType.Custom,
        universalTrackers: { $in: utrIds },
        initiativeId: { $in: childIds },
        // Does not deal with pulling data from shared groups
        // { 'share.initiativeId': { $in: childIds }, },
      },
      {
        groupData: 1,
        groupName: 1,
        universalTrackers: 1,
      } satisfies KeysEnum<MetricGroupMin, 1>)
      .lean<MetricGroupMin[]>()
      .sort({ created: -1 })
      .exec();
  }

  public static async getAllChildrenById<T = MetricGroupPlain>(
    metricGroupId: string | ObjectId,
    projection?: ProjectionType<T>
  ) {
    return MetricGroup.aggregate([
      {
        $match: { _id: metricGroupId },
      },
      {
        $graphLookup: {
          from: 'kpi-groups',
          startWith: '$_id',
          connectFromField: '_id',
          connectToField: 'parentId',
          as: 'subgroups',
        },
      },
      { $unwind: '$subgroups' },
      { $replaceRoot: { newRoot: '$subgroups' } },
      { $project: projection ?? { _id: 1, groupName: 1 } },
    ]).exec();
  }
}
