/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import type { KeysEnum } from '../models/public/projectionUtils';
import type { UniversalTrackerPlain } from '../models/universalTracker';

export type UtrMatchFilters = { $or: { [k: string]: unknown }[] } | undefined;

export type MinUtrLookup = Pick<UniversalTrackerPlain, '_id' | 'code'>;

export const minUtrProjection: KeysEnum<MinUtrLookup> = {
  _id: 1,
  code: 1,
};
