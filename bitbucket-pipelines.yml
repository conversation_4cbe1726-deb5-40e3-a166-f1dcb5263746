image: node:22-bookworm-slim

definitions:
  caches:
    npm: $HOME/.npm
    node-modules-cache: ./node_modules
  steps:
    - step: &ci-tests
        name: Run Tests
        caches:
          - node
          - npm
          - node-modules-cache
        script:
          # Install certificates first
          - apt-get update && apt-get install -y ca-certificates curl
          - echo "${GCP_GLOBAL_KEY_FILE_BASE64}" | base64 -d >> /tmp/key-file.json
          - export GOOGLE_APPLICATION_CREDENTIALS=/tmp/key-file.json
          - cp ./deploy/.initial_npmrc ~/.npmrc
          - apt-get update && apt-get -y install curl
          # Force consistent version for "mongodb-memory-server": "^10.1.2"
          - export MONGOMS_VERSION=7.0.3
          - npm run artifactregistry-login
          - npm install
          - npx tsc --noEmit
          - npm run test-codecov
          - ./deploy/codecov.sh
    - step: &pre-validation-nightly
        name: Pre-Validation on Nightly
        caches:
          - node
          - npm
        script:
          - /bin/sh deploy/pipe.sh
          - ./deploy/integration.sh https://api-nightly.g17.eco
    - step: &pre-validation-staging
        name: Pre-Validation on Staging
        caches:
          - node
        script:
          - /bin/sh deploy/pipe.sh
          - ./deploy/integration.sh https://api-staging.g17.eco
    - step: &pre-validation-demo
        name: Pre-Validation on Demo
        caches:
          - node
        script:
          - /bin/sh deploy/pipe.sh
          - ./deploy/integration.sh https://api.demo.g17.eco
    - step: &pre-validation-london
        name: Pre-Validation on London
        caches:
          - node
        script:
          - /bin/sh deploy/pipe.sh
          - ./deploy/integration.sh https://api.g17.eco
    - step: &pre-validation-uae
        name: Pre-Validation on UAE
        caches:
          - node
        script:
          - /bin/sh deploy/pipe.sh
          - ./deploy/integration.sh https://api-uae.g17.eco
    - step: &pre-validation-ksa
        name: Pre-Validation on KSA
        caches:
          - node
        script:
          - /bin/sh deploy/pipe.sh
          - ./deploy/integration.sh https://api-ksa.g17.eco
    - step: &pre-validation-singapore
        name: Pre-Validation on Singapore
        caches:
          - node
        script:
          - /bin/sh deploy/pipe.sh
          - ./deploy/integration.sh https://api.sg.g17.eco
    - step: &docker-build
        name: Build Docker Image
        image: node:22-bookworm
        services:
          - docker
        caches:
          - node-modules-cache
          - docker
        script:
          - ./deploy/build-image.sh ${BITBUCKET_BRANCH} ${BITBUCKET_BUILD_NUMBER}
    - step: &docker-promote-production
        name: Promote Docker Image Demo, London, Singapore, UAE, KSA
        image: google/cloud-sdk:alpine
        script:
          # - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} demo
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} production
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} london
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} singapore
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} uae
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} ksa
    - step: &docker-promote-nightly
        name: Promote Docker Image Nightly
        image: google/cloud-sdk:alpine
        script:
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} nightly
    - step: &docker-promote-staging
        name: Promote Docker Image Staging
        image: google/cloud-sdk:alpine
        script:
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} staging
    - step: &docker-promote-demo
        name: Promote Docker Image Demo
        image: google/cloud-sdk:alpine
        script:
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} demo
    - step: &docker-promote-london
        name: Promote Docker Image Production
        image: google/cloud-sdk:alpine
        script:
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} production
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} london
    - step: &docker-promote-singapore
        name: Promote Docker Image Singapore
        image: google/cloud-sdk:alpine
        script:
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} singapore
    - step: &docker-promote-uae
        name: Promote Docker Image UAE
        image: google/cloud-sdk:alpine
        script:
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} uae
    - step: &docker-promote-ksa
        name: Promote Docker Image KSA
        image: google/cloud-sdk:alpine
        script:
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} ksa
    - step: &docker-promote-loadtest
        name: Promote Docker Image Loadtest
        image: google/cloud-sdk:alpine
        script:
          - ./deploy/promote-image.sh ${BITBUCKET_BRANCH} loadtest
    - step: &docker-deploy-demo
        name: Deploy To Demo
        image: google/cloud-sdk:alpine
        deployment: demo
        script:
          - ./deploy/google-restart-instances.sh g17-eco-demo g17eco-tf-group-mig europe-west2
    - step: &docker-deploy-london
        name: Deploy To London
        image: google/cloud-sdk:alpine
        deployment: production
        script:
          - ./deploy/google-restart-instances.sh g17-eco g17eco-tf-group-mig europe-west2
    - step: &docker-deploy-singapore
        name: Deploy To Production (Singapore)
        image: google/cloud-sdk:alpine
        deployment: singapore
        script:
          - ./deploy/google-restart-instances.sh singapore-g17-eco g17eco-network-sg-group-mig asia-southeast1
    - step: &docker-deploy-demo-k8s
        name: Deploy To Demo (K8S)
        image: google/cloud-sdk:alpine
        deployment: demo
        services:
          - docker
        caches:
          - docker
        script:
          - pipe: atlassian/google-gke-kubectl-run:3.4.1
            variables:
              KEY_FILE: $GCP_GLOBAL_KEY_FILE_BASE64
              CLUSTER_NAME: g17eco-network-k8s
              COMPUTE_ZONE: europe-west2-b
              PROJECT: g17-eco-demo
              KUBECTL_COMMAND: 'rollout'
              KUBECTL_ARGS:
                - "restart"
                - "deployment/g17eco-api"
                - "-n"
                - "g17eco-api"
    - step: &docker-deploy-london-k8s
        name: Deploy To Production (London) (K8S)
        image: google/cloud-sdk:alpine
        deployment: production
        services:
          - docker
        caches:
          - docker
        script:
          - pipe: atlassian/google-gke-kubectl-run:3.4.1
            variables:
              KEY_FILE: $GCP_GLOBAL_KEY_FILE_BASE64
              CLUSTER_NAME: g17eco-network-k8s
              COMPUTE_ZONE: europe-west2-b
              PROJECT: g17-eco
              KUBECTL_COMMAND: 'rollout'
              KUBECTL_ARGS:
                - "restart"
                - "deployment/g17eco-api"
                - "-n"
                - "g17eco-api"
    - step: &docker-deploy-singapore-k8s
        name: Deploy To Production (Singapore) (K8S)
        image: google/cloud-sdk:alpine
        deployment: singapore
        services:
          - docker
        caches:
          - docker
        script:
          - pipe: atlassian/google-gke-kubectl-run:3.4.1
            variables:
              KEY_FILE: $GCP_GLOBAL_KEY_FILE_BASE64
              CLUSTER_NAME: g17eco-network-k8s
              COMPUTE_ZONE: asia-southeast1-c
              PROJECT: singapore-g17-eco
              KUBECTL_COMMAND: 'rollout'
              KUBECTL_ARGS:
                - "restart"
                - "deployment/g17eco-api"
                - "-n"
                - "g17eco-api"
    - step: &docker-deploy-uae
        name: Deploy To Production (UAE)
        deployment: uae
        services:
          - docker
        caches:
          - docker
        script:
          - pipe: atlassian/azure-aks-deploy:1.3.0
            variables:
              AZURE_APP_ID: $AZURE_CLIENT_ID
              AZURE_PASSWORD: $AZURE_CLIENT_SECRET
              AZURE_TENANT_ID: $AZURE_TENANT_ID
              AZURE_AKS_NAME: g17eco-network-aks
              AZURE_RESOURCE_GROUP: $AZURE_RESOURCE_GROUP
              KUBECTL_COMMAND: 'rollout'
              KUBECTL_ARGUMENTS: "restart deployment/g17eco-api -n g17eco-api"
    - step: &docker-deploy-ksa
        name: Deploy To Production (KSA)
        image: google/cloud-sdk:alpine
        deployment: ksa
        services:
          - docker
        caches:
          - docker
        script:
          - pipe: atlassian/google-gke-kubectl-run:3.4.1
            variables:
              KEY_FILE: $GCP_GLOBAL_KEY_FILE_BASE64
              CLUSTER_NAME: g17eco-network-k8s
              COMPUTE_ZONE: me-central2-c
              PROJECT: ksa-g17-eco
              KUBECTL_COMMAND: 'rollout'
              KUBECTL_ARGS:
                - "restart"
                - "deployment/g17eco-api"
                - "-n"
                - "g17eco-api"
    - step: &docker-deploy-nightly
        name: Deploy To Nightly
        image: google/cloud-sdk:alpine
        deployment: nightly
        services:
          - docker
        caches:
          - docker
          - node-modules-cache
        script:
          - pipe: atlassian/google-gke-kubectl-run:3.4.1
            variables:
              KEY_FILE: $GCP_GLOBAL_KEY_FILE_BASE64
              CLUSTER_NAME: g17eco-network-k8s
              COMPUTE_ZONE: europe-west2-b
              PROJECT: nightly-g17-eco
              KUBECTL_COMMAND: 'rollout'
              KUBECTL_ARGS:
                - "restart"
                - "deployment/g17eco-api"
                - "-n"
                - "g17eco-api"
    - step: &docker-deploy-staging
        name: Deploy To Staging2 (K8S)
        image: google/cloud-sdk:alpine
        services:
          - docker
        caches:
          - docker
          - node-modules-cache
        script:
          - pipe: atlassian/google-gke-kubectl-run:3.4.1
            variables:
              KEY_FILE: $GCP_GLOBAL_KEY_FILE_BASE64
              CLUSTER_NAME: g17eco-network-k8s
              COMPUTE_ZONE: europe-west2-b
              PROJECT: staging-g17-eco
              KUBECTL_COMMAND: 'rollout'
              KUBECTL_ARGS:
                - "restart"
                - "deployment/g17eco-api"
                - "-n"
                - "g17eco-api"
    - step: &docker-deploy-loadtest
        name: Deploy To Loadtest (K8S)
        image: google/cloud-sdk:alpine
        deployment: loadtest
        services:
          - docker
        caches:
          - docker
          - node-modules-cache
        script:
          - pipe: atlassian/google-gke-kubectl-run:3.4.1
            variables:
              KEY_FILE: $GCP_GLOBAL_KEY_FILE_BASE64
              CLUSTER_NAME: g17eco-network-k8s
              COMPUTE_ZONE: europe-west2-b
              PROJECT: loadtest-g17-eco
              KUBECTL_COMMAND: 'rollout'
              KUBECTL_ARGS:
                - "restart"
                - "deployment/g17eco-api"
                - "-n"
                - "g17eco-api"
pipelines:
  variables:
     - name: SKIP_VALIDATION
  default:
    - step: *ci-tests
  custom:
    0-validate-all:
      - variables:
        - name: SKIP_VALIDATION
      - parallel:
        - step: *pre-validation-nightly
        - step: *pre-validation-staging
        - step: *pre-validation-demo
        - step: *pre-validation-london
        - step: *pre-validation-uae
        - step: *pre-validation-ksa
        - step: *pre-validation-singapore
    0-docker-build:
      - step: *docker-build
    1-build-and-nightly:
      - variables:
          - name: SKIP_VALIDATION
      - step: *pre-validation-nightly
      - step: *docker-build
      - step: *docker-promote-nightly
      - step: *docker-deploy-nightly
    2-staging:
      - variables:
          - name: SKIP_VALIDATION
      - step: *pre-validation-staging
      - step: *docker-promote-staging
      - stage:
          name: Deploy Staging
          deployment: staging
          steps:
            - step: *docker-deploy-staging
    2-build-and-staging:
      - variables:
          - name: SKIP_VALIDATION
      - step: *docker-build
      - step: *pre-validation-staging
      - step: *docker-promote-staging
      - stage:
          name: Build and Deploy Staging
          deployment: staging
          steps:
            - step: *docker-deploy-staging
    3-all-production:
      - step: *docker-promote-production
      - parallel:
        # - step: *docker-deploy-demo
        - step: *docker-deploy-london
        - step: *docker-deploy-singapore
        - step: *docker-deploy-uae
        - step: *docker-deploy-ksa
    3a-only-demo:
      - variables:
          - name: SKIP_VALIDATION
      - step: *pre-validation-demo
      - step: *docker-promote-demo
      - parallel:
        - step: *docker-deploy-demo
        - step: *docker-deploy-demo-k8s
    3a-only-london:
      - variables:
          - name: SKIP_VALIDATION
      - step: *pre-validation-london
      - step: *docker-promote-london
      - parallel:
        - step: *docker-deploy-london
        - step: *docker-deploy-london-k8s
    3c-only-singapore:
      - variables:
          - name: SKIP_VALIDATION
      - step: *pre-validation-singapore
      - step: *docker-promote-singapore
      - parallel:
        - step: *docker-deploy-singapore
        - step: *docker-deploy-singapore-k8s
    3d-only-uae:
      - variables:
          - name: SKIP_VALIDATION
            default: false
            allowed-values:
             - true
             - false
      - step: *pre-validation-uae
      - step: *docker-promote-uae
      - step: *docker-deploy-uae
    3d-only-ksa:
      - variables:
          - name: SKIP_VALIDATION
            default: false
            allowed-values:
             - true
             - false
      - step: *pre-validation-ksa
      - step: *docker-promote-ksa
      - step: *docker-deploy-ksa
    4-loadtest:
      - step: *docker-promote-loadtest
      - step: *docker-deploy-loadtest
  branches:
    develop:
      - step: *ci-tests
      - step: *docker-build
      - step: *docker-promote-nightly
      - step: *docker-deploy-nightly
    release/*:
      - step: *ci-tests
      - step: *docker-build
      - parallel:
        - step: *pre-validation-nightly
        - step: *pre-validation-staging
        - step: *pre-validation-demo
        - step: *pre-validation-london
        - step: *pre-validation-singapore
