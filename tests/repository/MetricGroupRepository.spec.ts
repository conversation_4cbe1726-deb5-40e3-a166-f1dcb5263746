import { ObjectId } from 'bson';
import { expect } from 'chai';
import MetricGroup, { MetricGroupType } from '../../server/models/metricGroup';
import { MetricGroupRepository } from '../../server/repository/MetricGroupRepository';
import { createMetricGroup } from '../fixtures/metricGroupFixtures';
import { connect, disconnect } from '../setup/mongoInMemory';
import { clearFixtures } from '../setup/mongoInMemoryFixtures';

before(connect);
after(disconnect);

describe('MetricGroupRepository', () => {
  describe('getAllChildrenById', () => {
    afterEach(async () => {
      await clearFixtures([MetricGroup]);
    });

    it('should return empty array when parent does not exist', async () => {
      const nonExistentId = new ObjectId();
      const result = await MetricGroupRepository.getAllChildrenById(nonExistentId);
      expect(result).to.be.an('array');
      expect(result).to.have.length(0);
    });

    it('should return children metric groups from database', async () => {
      // Create parent metric group
      const parentId = new ObjectId();
      const parentGroup = new MetricGroup(
        createMetricGroup({
          _id: parentId,
          groupName: 'Parent Group',
          type: MetricGroupType.Custom,
        })
      );
      await parentGroup.save();

      // Create child metric groups
      const childId1 = new ObjectId();
      const childGroup1 = new MetricGroup(
        createMetricGroup({
          _id: childId1,
          groupName: 'Child Group 1',
          parentId: parentId,
          type: MetricGroupType.Custom,
        })
      );
      await childGroup1.save();

      const childId2 = new ObjectId();
      const childGroup2 = new MetricGroup(
        createMetricGroup({
          _id: childId2,
          groupName: 'Child Group 2',
          parentId: parentId,
          type: MetricGroupType.Custom,
        })
      );
      await childGroup2.save();

      // Create grandchild metric group
      const grandchildId = new ObjectId();
      const grandchildGroup = new MetricGroup(
        createMetricGroup({
          _id: grandchildId,
          groupName: 'Grandchild Group',
          parentId: childId1,
          type: MetricGroupType.Custom,
        })
      );
      await grandchildGroup.save();

      const result = await MetricGroupRepository.getAllChildrenById(parentId);

      expect(result).to.have.length(3);

      const resultGroupNames = result.map((group: any) => group.groupName).sort();
      expect(resultGroupNames).to.deep.equal(['Child Group 1', 'Child Group 2', 'Grandchild Group']);

      // Verify that all returned groups have the correct parent relationships
      const childGroups = result.filter((group: any) => group.parentId?.toString() === parentId.toString());
      const grandchildGroups = result.filter((group: any) => group.parentId?.toString() === childId1.toString());

      expect(childGroups).to.have.length(2);
      expect(grandchildGroups).to.have.length(1);
      expect(grandchildGroups[0].groupName).to.equal('Grandchild Group');
    });

    it('should return empty array when no children exist', async () => {
      // Create a metric group with no children
      const parentId = new ObjectId();
      const parentGroup = new MetricGroup(
        createMetricGroup({
          _id: parentId,
          groupName: 'Lonely Parent Group',
          type: MetricGroupType.Custom,
        })
      );
      await parentGroup.save();

      const result = await MetricGroupRepository.getAllChildrenById(parentId);

      expect(result).to.be.an('array');
      expect(result).to.have.length(0);
    });
  });
});
