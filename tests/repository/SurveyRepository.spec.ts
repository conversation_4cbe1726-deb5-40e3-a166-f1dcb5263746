/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { SurveyRepository } from '../../server/repository/SurveyRepository';
import { createSandbox } from 'sinon';
import { expect } from 'chai';
import { initiativeOneSimple } from '../fixtures/initiativeFixtures';
import { userOne } from '../fixtures/userFixtures';
import { ObjectId } from 'bson';
import { InitiativeRepository } from '../../server/repository/InitiativeRepository';
import { child21, child211, child2111, mockInitiativeTree } from '../fixtures/initiativeTreeFixtures';
import { UserRoles } from '../../server/service/user/userPermissions';
import { metricGroupOne, metricGroupOneUtr } from '../fixtures/metricGroupFixtures';
import { createBlueprintWithUtrGroups } from '../fixtures/blueprintFixtures';
import { GroupType } from '../../server/survey/utrGroupConfigs';
import {
  universalTrackerOne as addedUtr,
  gri2020Sdg1GroupQuestion as removedUtr,
} from '../fixtures/universalTrackerFixtures';

describe.only('SurveyRepository', () => {
  describe('getInitiativePermissionIds', () => {
    const sandbox = createSandbox();
    const parentOneId = new ObjectId();
    const parentTwoId = new ObjectId();
    const mockInitiativeWithParents = {
      ...initiativeOneSimple,
      parents: [
        { ...initiativeOneSimple, _id: parentOneId },
        { ...initiativeOneSimple, _id: parentTwoId },
      ],
    };
    const mockInitiativeIds = mockInitiativeTree.map((i) => i._id);

    beforeEach(() => {
      sandbox.stub(InitiativeRepository, 'getAllParentsById').resolves([mockInitiativeWithParents]);
    });

    afterEach(() => sandbox.restore());

    const testCases = [
      {
        desc: 'When user does not have any permissions',
        input: {
          user: { ...userOne, permission: [] },
        },
        output: [],
      },
      {
        desc: 'When user does not have any contributor or verifier permission',
        input: {
          user: { ...userOne, permission: [{ initiativeId: parentOneId, permissions: [UserRoles.User] }] },
        },
        output: [],
      },
      {
        desc: 'When user has a parent contributor permission',
        input: {
          user: { ...userOne, permission: [{ initiativeId: parentTwoId, permissions: [UserRoles.Contributor] }] },
        },
        output: mockInitiativeIds,
      },
      {
        desc: 'When user has a parent verifier permission',
        input: {
          user: { ...userOne, permission: [{ initiativeId: parentOneId, permissions: [UserRoles.Verifier] }] },
        },
        output: mockInitiativeIds,
      },
      {
        desc: 'When user has a specific node permission',
        input: {
          user: { ...userOne, permission: [{ initiativeId: child21._id, permissions: [UserRoles.Verifier, UserRoles.Contributor] }] },
        },
        output: [child21._id, child211._id, child2111._id],
      },
    ];

    testCases.forEach(({ desc, input: { user }, output }) => {
      it(desc, async () => {
        const result = await SurveyRepository.getInitiativePermissionIds({
          user,
          initiativeIds: mockInitiativeIds,
          initiatives: mockInitiativeTree,
        });
        expect(result.every((item) => output.some((i) => i.equals(item)))).to.eq(true);
      });
    });
  });

  describe('generateScopeUpdates', () => {
    const blueprint = createBlueprintWithUtrGroups({ utrGroups: [metricGroupOneUtr] });
    // metricGroupOne has 1 utr called removedUtr
    const customMetricGroups = [metricGroupOne];

    it('should return empty array if nothing get updated', () => {
      const scopeUpdates = SurveyRepository.generateScopeUpdates(customMetricGroups, blueprint, blueprint);
      expect(scopeUpdates).to.deep.eq([]);
    });

    it('should return scope updates if Blueprint gets updated', () => {
      const blueprintWithDate = { ...blueprint, date: new Date('2024-03-31') };
      const defaultBlueprintWithDate = { ...blueprint, date: new Date('2024-01-31') };
      const scopeUpdates = SurveyRepository.generateScopeUpdates(
        customMetricGroups,
        blueprintWithDate,
        defaultBlueprintWithDate
      );
      expect(scopeUpdates).to.deep.eq([
        { type: GroupType.Custom, name: 'Standards And Frameworks', added: [], removed: [] },
      ]);
    });

    describe('custom metric groups updates', () => {
      const testCases = [
        {
          label: 'new metrics are added to group',
          customMetricGroups: [
            {
              ...metricGroupOne,
              universalTrackers: [...metricGroupOne.universalTrackers, addedUtr._id],
              universalTracker: [...(metricGroupOne.universalTracker ?? []), addedUtr],
            },
          ],
          expectedScopeUpdates: [
            { type: GroupType.Group, name: metricGroupOne.groupName, added: [addedUtr.code], removed: [] },
          ],
        },
        {
          label: 'existing metrics are removed from group',
          customMetricGroups: [
            {
              ...metricGroupOne,
              universalTrackers: [],
              universalTracker: [],
            },
          ],
          expectedScopeUpdates: [
            { type: GroupType.Group, name: metricGroupOne.groupName, added: [], removed: [removedUtr.code] },
          ],
        },
        {
          label: 'new metrics are added & existing metrics are removed',
          customMetricGroups: [
            {
              ...metricGroupOne,
              universalTrackers: [addedUtr._id],
              universalTracker: [addedUtr],
            },
          ],
          expectedScopeUpdates: [
            {
              type: GroupType.Group,
              name: metricGroupOne.groupName,
              added: [addedUtr.code],
              removed: [removedUtr.code],
            },
          ],
        },
      ];

      testCases.forEach(({ label, customMetricGroups, expectedScopeUpdates }) => {
        it(`should return scope updates if ${label}`, () => {
          const scopeUpdates = SurveyRepository.generateScopeUpdates(customMetricGroups, blueprint, blueprint);
          expect(scopeUpdates).to.deep.eq(expectedScopeUpdates);
        });
      });
    });

    describe('custom metric group subgroups updates', () => {
      // Create parent metric group with subgroups
      const parentGroupId = new ObjectId();
      const subgroupId1 = new ObjectId();

      const parentMetricGroup = {
        ...metricGroupOne,
        _id: parentGroupId,
        groupName: 'Parent Group',
        subgroups: [
          {
            ...metricGroupOne,
            _id: subgroupId1,
            groupName: 'Subgroup',
            parentId: parentGroupId,
            universalTrackers: metricGroupOne.universalTrackers,
            universalTracker: metricGroupOne.universalTracker,
          },
        ],
      };

      const blueprintWithSubgroups = createBlueprintWithUtrGroups({
        utrGroups: [
          metricGroupOneUtr,
          {
            type: GroupType.Group,
            groupId: subgroupId1,
            groupDate: new Date(),
            utrCodes: metricGroupOneUtr.utrCodes,
            groupName: 'Subgroup',
            groupData: metricGroupOne.groupData,
          },
        ],
      });

      it('should return empty array if nothing get updated', () => {
        const customMetricGroupsWithSubgroups = [parentMetricGroup];
        const scopeUpdates = SurveyRepository.generateScopeUpdates(
          customMetricGroupsWithSubgroups,
          blueprintWithSubgroups,
          blueprintWithSubgroups
        );
        expect(scopeUpdates).to.deep.eq([]);
      });

      it('should return scope updates if subgroup metrics get updated', () => {
        // Update subgroup 1 to add a new metric and subgroup 2 to add a metric
        const updatedParentMetricGroup = {
          ...parentMetricGroup,
          universalTrackers: [addedUtr._id],
          universalTracker: [addedUtr],
          subgroups: [
            {
              ...parentMetricGroup.subgroups?.[0],
              universalTrackers: [addedUtr._id],
              universalTracker: [addedUtr],
            },
          ],
        };

        const customMetricGroupsWithSubgroups = [updatedParentMetricGroup];
        const scopeUpdates = SurveyRepository.generateScopeUpdates(
          customMetricGroupsWithSubgroups,
          blueprintWithSubgroups,
          blueprintWithSubgroups
        );

        expect(scopeUpdates).to.deep.eq([
          { type: GroupType.Group, name: 'Subgroup 1', added: [addedUtr.code], removed: [] },
        ]);
      });
    });
  });
});
