/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { expect } from 'chai';
import { userOne, userTwo } from '../../fixtures/userFixtures';
import { type DelegationScope } from '../../../server/models/survey';
import { SurveyScope } from '../../../server/service/survey/SurveyScope';
import { type StakeholderGroup } from '../../../server/models/stakeholderGroup';
import { surveyOne } from '../../fixtures/survey';
import {
  applyOnboardingDelegationScope,
  createChildGroup,
  extractSurveyIds,
  fromDelegationScope,
  getScopeGroup,
  isDelegationScopeEmpty,
  processScopeChange,
  processScopeGroups,
} from '../../../server/service/survey/surveyDelegationProcess';
import { ObjectId } from 'bson';
import { Actions } from '../../../server/service/action/Actions';
import {
  type BaseScopeChange,
  type DelegationScopeUser,
  type RequestScope,
} from '../../../server/service/survey/model/DelegationScope';
import User from '../../../server/models/user';
import { getGroup, getGroupChildrenTags, getSubGroup, type Group } from '@g17eco/core';
import { createMetricGroup } from '../../fixtures/metricGroupFixtures';

describe('surveyDelegationProcess', () => {

  const griGroupOneCode = 'gri-1';
  const griGroupOneSubgroupOneCode = `${griGroupOneCode}-101`;

  const createGroup = (id: ObjectId): StakeholderGroup => ({
    stakeholder: [id],
    verifier: [],
    escalation: [],
  });

  describe('extractSurveyIds', function () {
    it('should extract deeply nested ids', function () {

      const id = new ObjectId();
      const innerId = new ObjectId();
      const innerChildren = [{
        ...createChildGroup(griGroupOneSubgroupOneCode),
        stakeholder: [innerId]
      }];

      const obDelegationScope: DelegationScope = {
        ...SurveyScope.createEmptyDelegationScope(),
        sdg: { '4': createGroup(surveyOne._id), },
        standards: {
          gri: {
            ...getScopeGroup(),
            children: [
              {
                ...createChildGroup(griGroupOneCode),
                stakeholder: [surveyOne._id, id],
                children: innerChildren
              }
            ]
          }
        },
      }

      const ids = extractSurveyIds(obDelegationScope)
      expect(ids).to.be.lengthOf(3, 'Expected 3 unique ids');

      const surveyIds = ids.map(String);
      expect(surveyIds).contain(String(id), 'First level children id to be included')
      expect(surveyIds).contain(String(innerId), 'Second level children id to be included')
    });
  });

  describe('isDelegationScopeEmpty', function () {

    it('should check top level ids', function () {
      const obDelegationScope: DelegationScope = {
        ...SurveyScope.createEmptyDelegationScope(),
        sdg: { '4': createGroup(surveyOne._id), },
      }
      const ids = isDelegationScopeEmpty(obDelegationScope)
      expect(ids).to.false;
    });


    it('should check top level group', function () {
      const ids = isDelegationScopeEmpty(SurveyScope.createEmptyDelegationScope())
      expect(ids).to.true;
    });

    it('should check deeply nested ids', function () {
      const obDelegationScope: DelegationScope = {
        ...SurveyScope.createEmptyDelegationScope(),
        standards: {
          gri: {
            ...getScopeGroup(),
            children: [
              {
                ...createChildGroup(griGroupOneCode),
                children: [{ ...createChildGroup(griGroupOneSubgroupOneCode), stakeholder: [new ObjectId()] }]
              }
            ]
          }
        },
      }
      expect(isDelegationScopeEmpty(obDelegationScope)).to.false;
    });
  });

  describe('fromDelegationScope', function () {

    it('recreate scope from delegation scope', function () {
      const id = new ObjectId('6011f43adc1cfb37b5327d62');
      const obDelegationScope: DelegationScope = {
        ...SurveyScope.createEmptyDelegationScope(),
        sdg: { '4': createGroup(id), },
        standards: {
          gri: {
            ...getScopeGroup(),
            stakeholder: [id],
            children: [
              {
                ...createChildGroup(griGroupOneCode),
                stakeholder: [id],
                children: [{
                  ...createChildGroup(griGroupOneSubgroupOneCode),
                  stakeholder: [id]
                }]
              }
            ]
          }
        },
      }

      const scope = fromDelegationScope(obDelegationScope, 'stakeholder', id)
      expect(scope.sdg).eqls(['4'])
      expect(scope.standards).eqls(['gri', griGroupOneCode, griGroupOneSubgroupOneCode])
    });

    it('scope from delegation scope only sub groups', function () {
      const id = new ObjectId('6011f43adc1cfb37b5327d62');
      const obDelegationScope: DelegationScope = {
        ...SurveyScope.createEmptyDelegationScope(),
        standards: {
          gri: {
            ...getScopeGroup(),
            children: [
              {
                ...createChildGroup(griGroupOneCode),
                children: [{ ...createChildGroup(griGroupOneSubgroupOneCode), stakeholder: [id] }]
              }
            ]
          }
        },
      }
      const scope = fromDelegationScope(obDelegationScope, 'stakeholder', id)
      expect(scope.standards).eqls([griGroupOneSubgroupOneCode])
    });
  });


  describe('processScopeGroups fn', function () {

    const delegator = new User(userOne);
    const userId = userTwo._id.toHexString();
    const role: keyof StakeholderGroup = 'stakeholder'

    describe('standards GRI  ', function () {

      const scopeType = 'standards';
      const standardCode = 'gri';
      const obDelegationScope: DelegationScope = {
        ...SurveyScope.createEmptyDelegationScope(),
        [scopeType]: {
          [standardCode]: {
            ...getScopeGroup(),
            children: [
              { ...createChildGroup(griGroupOneCode), [role]: [userTwo._id] }
            ]
          }
        },
      }

      const getRequest = (scopeGroups: RequestScope[], action = Actions.Add): DelegationScopeUser => ({
        action,
        surveyId: surveyOne._id,
        delegator,
        role,
        scopeGroups,
        domain: undefined,
        userId,
      });

      const group = getGroup(scopeType, standardCode) as Group

      it('should add delegation for whole gri', function () {
        const request: DelegationScopeUser = getRequest([{
          code: standardCode,
          scopeType,
          scopeTags: [],
        }])
        const result = processScopeGroups(obDelegationScope, request);


        const scopeSection = result[scopeType][standardCode];
        expect(scopeSection[role]).lengthOf(1);
        expect(scopeSection.children).lengthOf(group?.subgroups?.length as number)

        const childrenIds = scopeSection?.children?.reduce((a, c) => {
          a.push(...c[role])
          return a;
        }, [] as ObjectId[])
        expect(childrenIds).lengthOf(group?.subgroups?.length as number);

        // Deep nested children
        scopeSection.children?.forEach(c => {
          const subGroup = getSubGroup(group, c.code);
          expect(c.children).lengthOf(subGroup?.subgroups?.length as number)
          // Third level
          c?.children?.forEach(leafChild => {
            expect(leafChild[role].map(String)).contains(userId)
          })
        })
      });

      it('should remove delegation for whole gri', function () {
        const request: DelegationScopeUser = getRequest([{
          code: standardCode,
          scopeType,
          scopeTags: [],
        }], Actions.Remove)
        const result = processScopeGroups(obDelegationScope, request);
        const scopeSection = result[scopeType][standardCode];

        expect(scopeSection[role]).lengthOf(0);
        scopeSection?.children?.forEach(c => {
          c?.children?.forEach(leafChild => {
            expect(leafChild[role].map(String)).not.contains(userId)
          })
        })
      });

      it('should first children', function () {
        const subGroupCodes = group?.subgroups?.map(g => g.code).slice(0, 2) ?? [];
        const request: DelegationScopeUser = getRequest([{
          code: standardCode,
          scopeType,
          scopeTags: subGroupCodes,
        }], Actions.Add)
        const result = processScopeGroups(obDelegationScope, request);
        const scopeSection = result[scopeType][standardCode];
        expect(scopeSection[role]).lengthOf(0);

        scopeSection.children?.forEach(c => {
          const ids = c[role].map(String);
          if (subGroupCodes.includes(c.code)) {
            expect(ids).contains(userId)
          } else {
            expect(ids).not.contains(userId)
          }
        })
      });

      it('should second children', function () {
        const childGroup = group.subgroups?.[0];
        const subGroupCodes = childGroup?.subgroups?.map(g => g.code).slice(0, 2);
        const request: DelegationScopeUser = getRequest([{
          code: standardCode,
          scopeType,
          scopeTags: subGroupCodes,
        }], Actions.Add)
        const result = processScopeGroups(obDelegationScope, request);
        const scopeSection = result[scopeType][standardCode];
        expect(scopeSection[role]).lengthOf(0);

        const scopeChildGroup = scopeSection.children?.find(c => c.code === childGroup?.code)
        scopeChildGroup?.children?.forEach(c => {
          const ids = c[role].map(String);
          if (subGroupCodes?.includes(c.code)) {
            expect(ids).contains(userId)
          } else {
            expect(ids).not.contains(userId)
          }
        })
      });
    })
  });

  describe('onboardDelegationScope', function () {

    it('should onboard scope ', async () => {

      const obDelegationScope: DelegationScope = {
        ...SurveyScope.createEmptyDelegationScope(),
        sdg: {
          '3': {
            ...getScopeGroup(),
            children: [{
              ...createChildGroup('3.1'),
              stakeholder: [surveyOne._id]
            }]
          },
          '4': createGroup(surveyOne._id),
          '5': {
            ...getScopeGroup(),
            children: [{
              ...createChildGroup('5.1'),
              stakeholder: [surveyOne._id]
            }]
          },
        },
        standards: {
          gri: {
            ...getScopeGroup(),
            children: [{ ...createChildGroup('gri-1'), stakeholder: [surveyOne._id] }]
          }
        },
      }

      const { hasChanged, surveyDelegationScope } = applyOnboardingDelegationScope({
        surveyId: surveyOne._id,
        surveyDelegationScope: {
          ...SurveyScope.createEmptyDelegationScope(),
          sdg: { '3': createGroup(userTwo._id) },
          standards: { gri: createGroup(userOne._id) },
        },
        delegationScope: obDelegationScope,
        obUserId: userOne._id.toHexString()
      });

      expect(hasChanged).to.be.true

      const { sdg, standards } = surveyDelegationScope;

      const userId = userOne._id;
      expect(sdg['4']).to.be.eqls({
        ...getScopeGroup(),
        stakeholder: [userId],
      })

      expect(sdg['3']).to.be.eqls({
        ...createGroup(userTwo._id),
        children: [{ ...createChildGroup('3.1'), stakeholder: [userId] }]
      })

      expect(standards.gri).to.be.eqls({
        ...createGroup(userOne._id),
        children: [{
          ...createChildGroup(griGroupOneCode), stakeholder: [userId]
        }]
      })
    });
  });

  describe('processScopeChange fn', function () {

    const createScopeRequest = (
      scopeGroups: RequestScope[],
      action = Actions.Add
    ): BaseScopeChange => ({ action, scopeGroups });

    const createSimpleScopeRequest = (
      scopeTags: string[],
      scopeType: keyof DelegationScope,
      action = Actions.Add
    ): BaseScopeChange => ({ action, scopeGroups: scopeTags.map(code => ({ code, scopeType })) })

    describe('simple add scope', function () {

      it('should add materiality', function () {
        const materiality = ['high', 'medium'];
        const data = createSimpleScopeRequest(materiality, 'materiality');
        const result = processScopeChange({ data });
        expect(result.materiality).to.eqls(materiality);
      });

      // if no metricGroups provided, should add custom group _id like before
      it('should add custom objectId update', function () {
        const id = String(new ObjectId());
        const data = createSimpleScopeRequest([id], 'custom');
        const { custom } = processScopeChange({ data });
        expect(custom.map(String)).to.contain(id);
      });
    });

    describe('simple remove scope', function () {

      it('should remove materiality', function () {
        const materiality = ['high', 'medium'];
        const data = createSimpleScopeRequest(materiality, 'materiality', Actions.Remove)
        const surveyScope = { ...SurveyScope.createEmpty(), materiality };
        const result = processScopeChange({ data, surveyScope })
        expect(result.materiality).to.eqls([])
      });

      it('should remove custom objectId update', function () {
        const objectId = new ObjectId();
        const idToRemain = new ObjectId();
        const data = createSimpleScopeRequest([String(objectId)], 'custom', Actions.Remove)
        const surveyScope = { ...SurveyScope.createEmpty(), custom: [objectId, idToRemain] };
        const { custom } = processScopeChange({ data, surveyScope })
        expect(custom.map(String)).eqls([idToRemain.toHexString()])
      });
    });

    describe('add scope', function () {

      const scopeType = 'standards';
      const groupCode = 'gri';
      const group = getGroup(scopeType, groupCode) as Group;
      const [first] = group?.subgroups ?? [];

      it('should add kpi standard', () => {
        const kpiCode = 'kpi';
        const data = createScopeRequest([{ code: kpiCode, scopeType }])
        const { standards } = processScopeChange({ data })
        expect(standards).to.contain(kpiCode)
      });

      it('should add top level', function () {
        const data = createScopeRequest([{ code: groupCode, scopeType }])
        const { standards } = processScopeChange({ data })
        expect(standards).to.contain(groupCode)
      });

      it('should add child level', function () {
        const data = createScopeRequest(
          [{ code: groupCode, scopeType, scopeTags: [first.code] }],
        )
        const { standards } = processScopeChange({ data })
        const value = [first.code, ...getGroupChildrenTags(first)];
        expect(standards).eqls(value)
      });

      it('should add sub-child level', function () {
        const codes = group.subgroups?.map(g => g.code) ?? [];
        const code = 'blabs';
        const data = createScopeRequest(
          [
            { code: code, scopeType, scopeTags: [] },
            { code: groupCode, scopeType, scopeTags: codes },
          ],
        );
        const { standards } = processScopeChange({ data })
        const subGroupCodes = group.subgroups?.map(g => [g.code, ...getGroupChildrenTags(g)]).flat() ?? [];
        expect(standards).eqls([code, ...subGroupCodes])
      });
    });

    describe('remove scope', function () {

      const action = Actions.Remove;
      const scopeType = 'standards';
      const groupCode = 'gri';
      const group = getGroup(scopeType, groupCode) as Group;
      const [first, second] = group.subgroups ?? [];
      const [firstSubChild, secondSubChild] = first.subgroups ?? [];
      const subGroupCodes = group.subgroups?.map(g => [g.code, ...getGroupChildrenTags(g)]).flat() ?? [];

      it('should remove kpi standard', () => {
        const kpiCode = 'kpi';
        const surveyScope = { ...SurveyScope.createEmpty(), standards: [kpiCode] };
        const data = createScopeRequest([{ code: kpiCode, scopeType }], action)
        const { standards } = processScopeChange({ data, surveyScope })
        expect(standards).eqls([])
      });

      it('should update first-child level', function () {
        const removedCodes = [group.code, first.code, ...first?.subgroups?.map(g => g.code) ?? []]
        const surveyScope = { ...SurveyScope.createEmpty(), standards: [group.code, ...subGroupCodes] };
        const data = createScopeRequest(
          [{ code: groupCode, scopeType, scopeTags: [first.code] }],
          action,
        );

        const expectedScope = subGroupCodes.filter(code => !removedCodes.includes(code))

        const { standards } = processScopeChange({ data, surveyScope })
        expect(standards).eqls(expectedScope)
      });

      it('should update sub-child level', function () {
        const surveyScope = { ...SurveyScope.createEmpty(), standards: subGroupCodes };
        const data = createScopeRequest(
          [{ code: groupCode, scopeType, scopeTags: [firstSubChild.code] }],
          action,
        );

        const removedCodes: string[] = [groupCode, first.code, firstSubChild.code]
        const expectedScope = subGroupCodes.filter(code => !removedCodes.includes(code))

        const { standards } = processScopeChange({ data, surveyScope })
        expect(standards).eqls(expectedScope)
      });


      it('not-valid code shout not update scope', function () {
        const surveyScope = { ...SurveyScope.createEmpty(), standards: subGroupCodes };
        const data = createScopeRequest(
          [{ code: groupCode, scopeType, scopeTags: ['1233'] }],
          action,
        );

        const { standards } = processScopeChange({ data, surveyScope })
        expect(standards).eqls(subGroupCodes)
      });

      it('not-valid code shout not remove top level scope', function () {
        const surveyScope = { ...SurveyScope.createEmpty(), standards: [groupCode] };
        const data = createScopeRequest(
          [{ code: groupCode, scopeType, scopeTags: ['1233ssss'] }],
          action,
        );

        const { standards } = processScopeChange({ data, surveyScope })
        expect(standards).eqls([groupCode])
      });

      it('Top level code, removing deep child first', function () {
        const surveyScope = { ...SurveyScope.createEmpty(), standards: [groupCode] };
        const data = createScopeRequest(
          [{ code: groupCode, scopeType, scopeTags: [firstSubChild.code] }],
          action,
        );

        const { standards } = processScopeChange({ data, surveyScope })
        const expectInclude = [secondSubChild.code, ...(getGroupChildrenTags(second))];
        expect(standards).include.members(expectInclude)
      });

      it('Sub level code, removing deep only', function () {
        const surveyScope = { ...SurveyScope.createEmpty(), standards: [first.code] };
        const data = createScopeRequest(
          [{ code: groupCode, scopeType, scopeTags: [firstSubChild.code] }],
          action,
        );

        const removedCodes = [firstSubChild.code];
        const expectedScope = getGroupChildrenTags(first)
          .filter(code => !removedCodes.includes(code));

        const { standards } = processScopeChange({ data, surveyScope })
        expect(standards).eqls(expectedScope)
      });

      it('Top level code, removing deep child', function () {
        const surveyScope = { ...SurveyScope.createEmpty(), standards: [groupCode] };
        const data = createScopeRequest(
          [{ code: groupCode, scopeType, scopeTags: [firstSubChild.code] }],
          action,
        );

        const allGroupCodes = getGroupChildrenTags(group)
        const removedCodes: string[] = [groupCode, first.code, firstSubChild.code]
        // All other parts should be added
        const expectedScope = allGroupCodes.filter(code => !removedCodes.includes(code))

        const { standards } = processScopeChange({ data, surveyScope })
        expect(standards).eqls(expectedScope)
      });

    });

    describe('custom scope with metricGroups', function () {
      const [groupId, childGroupOneId, grandChildGroupOneId, childGroupTwoId] = Array.from({ length: 4 }).map(() => new ObjectId());
      const group = createMetricGroup({ _id: groupId });
      const childGroup = createMetricGroup({ _id: childGroupOneId, parentId: groupId });
      const grandChildGroup = createMetricGroup({ _id: grandChildGroupOneId, parentId: childGroupOneId });
      const childGroupTwo = createMetricGroup({ _id: childGroupTwoId, parentId: groupId });

      const metricGroups = [
        {
          ...group,
          subgroups: [{ ...childGroup, subgroups: [grandChildGroup] }, childGroupTwo],
        },
      ];

      describe('add scope', () => {
        it('should add custom group with no children', () => {
          const data = createScopeRequest([{ code: String(groupId), scopeType: 'custom' }], Actions.Add);
          const surveyScope = { ...SurveyScope.createEmpty(), custom: [] };
          const { custom } = processScopeChange({ data, surveyScope, metricGroups: [group] });
          expect(custom).to.deep.equal([groupId]);
        });

        it('should add custom group (parent) with its children', () => {
          const data = createScopeRequest([{ code: String(groupId), scopeType: 'custom' }], Actions.Add);
          const surveyScope = { ...SurveyScope.createEmpty(), custom: [] };
          const { custom } = processScopeChange({ data, surveyScope, metricGroups });
          expect(custom).to.deep.equal([groupId, childGroupOneId, grandChildGroupOneId, childGroupTwoId]);
        });

        it('should add custom group (child) with its children', () => {
          const data = createScopeRequest([{ code: String(groupId), scopeType: 'custom', scopeTags: [String(childGroupOneId)] }], Actions.Add);
          const surveyScope = { ...SurveyScope.createEmpty(), custom: [] };
          const { custom } = processScopeChange({ data, surveyScope, metricGroups });
          expect(custom).to.deep.equal([childGroupOneId, grandChildGroupOneId]);
        });
      });

      describe('remove scope', () => {
        it('should remove custom group', () => {
          const data = createSimpleScopeRequest([String(groupId)], 'custom', Actions.Remove);
          const surveyScope = { ...SurveyScope.createEmpty(), custom: [groupId] };
          const { custom } = processScopeChange({ data, surveyScope, metricGroups: [group] });
          expect(custom).to.deep.equal([]);
        });

        it('should remove custom group and its children', () => {
          const data = createSimpleScopeRequest([String(groupId)], 'custom', Actions.Remove);
          const surveyScope = {
            ...SurveyScope.createEmpty(),
            custom: [groupId, childGroupOneId, grandChildGroupOneId, childGroupTwoId],
          };
          const { custom } = processScopeChange({ data, surveyScope, metricGroups });
          expect(custom).to.deep.equal([]);
        });

        it('should remove custom group, its children and parent', () => {
          const data = createScopeRequest([{ code: String(groupId), scopeType: 'custom', scopeTags: [String(childGroupOneId)] }], Actions.Remove);
          const surveyScope = {
            ...SurveyScope.createEmpty(),
            custom: [groupId, childGroupOneId, grandChildGroupOneId, childGroupTwoId],
          };
          const { custom } = processScopeChange({ data, surveyScope, metricGroups });
          expect(custom).to.deep.equal([childGroupTwoId]);
        });
      });
    });
  });
});
