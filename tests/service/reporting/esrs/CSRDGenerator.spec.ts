/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { createSandbox } from 'sinon';
import { testLogger } from '../../../factories/logger';
import {
  CsrdReportGenerator,
  getCsrdReportGenerator,
} from '../../../../server/service/reporting/csrd/CsrdReportGenerator';
import { surveyOne } from '../../../fixtures/survey';
import { createInitiative } from '../../../fixtures/initiativeFixtures';
import { getXhtmlGenerator } from '../../../../server/service/reporting/xhtml/XHTMLGenerator';
import UniversalTrackerValue from '../../../../server/models/universalTrackerValue';
import setup from '../../../setup';
import { esrs2_E3_4 } from '../../../fixtures/utr/utrTableFixtures';

describe('CsrdReportGenerator', () => {
  const sandbox = createSandbox();
  const xmlGenerator = getXhtmlGenerator();
  const csrdGenerator = new CsrdReportGenerator(testLogger, xmlGenerator);

  const initiative = createInitiative({
    name: 'Alpha Bravo Ltd.',
  });

  beforeEach(() => {});

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    const instance = getCsrdReportGenerator();
    expect(instance).to.be.instanceOf(CsrdReportGenerator);
  });

  it('process report and return status', async () => {
    setup.wrapStub(sandbox, UniversalTrackerValue, 'aggregate', () => [esrs2_E3_4]);

    const result = await csrdGenerator.generate({
      survey: surveyOne,
      initiative: initiative,
      mapping: {},
      preview: true,
      sections: [
        {
          header: { title: 'Test Section with Table' },
          children: [
            {
              type: 'table',
              columns: [{ id: 'col1', name: 'Column 1', accessor: 'col1' }],
              data: [
                {
                  col1: {
                    type: 'fact',
                    tag: 'ix:nonFraction',
                    name: 'esrs:WaterConsumption',
                    id: 'fact-1',
                    contextRef: 'c-1',
                    unitRef: 'u-101',
                    decimals: 0,
                    children: [{ type: 'inline', content: '100' }],
                  },
                },
              ],
            },
          ],
        },
      ],
    });

    const { xmlString } = result;

    expect(xmlString).to.contain(initiative.name);
  });

  it('should generate report with empty sections', async () => {
    const result = await csrdGenerator.generate({
      survey: surveyOne,
      initiative: initiative,
      mapping: {},
      preview: true,
      sections: [],
    });

    const { xmlString } = result;
    expect(xmlString).to.contain(initiative.name);
    expect(xmlString).to.not.include('<ix:nonFraction');
  });
});
