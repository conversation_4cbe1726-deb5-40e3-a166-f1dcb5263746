
import Initiative from '../../server/models/initiative';
import Survey, { SurveyModelPlain } from '../../server/models/survey';
import { UtrValueType } from '../../server/models/public/universalTrackerType';
import { createUtrFromCode } from '../fixtures/universalTrackerFixtures';
import { createUtrv } from '../fixtures/compositeUTRVFixtures';

import { ObjectId } from 'bson';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from '../../server/models/universalTrackerValue';
import UniversalTracker, { UniversalTrackerPlain } from '../../server/models/universalTracker';
import User, { UserPlain } from '../../server/models/user';
import { surveyOne } from '../fixtures/survey';
import { UserRoles } from '../../server/service/user/userPermissions';
import BackgroundJob, { BackgroundJobPlain } from '../../server/models/backgroundJob';
import { createSimpleDashboard } from '../fixtures/dashboardFixtures';
import { InsightDashboardPlain } from '../../server/models/insightDashboard';
import { InsightDashboard } from '../../server/models/insightDashboard';
import Document, { DocumentPlain } from '../../server/models/document';
import { createDocument } from '../fixtures/documentFixtures';
import { SurveyTemplate, SurveyTemplatePlain } from '../../server/models/surveyTemplate';
import { SurveyTemplateHistory, SurveyTemplateHistoryModelPlain } from '../../server/models/surveyTemplateHistory';
import Organization, { OrganizationPlain } from '../../server/models/organization';
import MetricGroup, { MetricGroupPlain } from '../../server/models/metricGroup';
import { Model } from 'mongoose';

export interface InitiativeTreeFixture {
  id: ObjectId;
  surveyFixture?: SurveyFixtureConfig;
  children?: InitiativeTreeFixture[];
}

export interface UtrvFixtureConfig {
  utrv: Partial<
    Pick<UniversalTrackerValuePlain, 'valueType' | 'value' | 'initiativeId' | 'status' | 'period' | 'effectiveDate' | 'stakeholders' | 'valueData'>
  >;
  utr: UniversalTrackerPlain;
}

export interface UsersFixtureConfig {
  _id?: ObjectId;
  oktaUserId?: string;
  name: string;
  email: string;
  roles: UserRoles[];
}
export interface InitiativeFixtureConfig {
  _id: ObjectId,
  depth: number,
  children: number,
  surveys: number,
  rootUsersConfig: UsersFixtureConfig[],
  utrvsConfig: UtrvFixtureConfig[]
}

export type SurveyFixtureConfig = Partial<Pick<SurveyModelPlain, 'period' | 'effectiveDate'>>;

export const setupUsers = async (currentInitiativeId: ObjectId, rootUsersConfig: UsersFixtureConfig[]) => {
  for (const { _id, oktaUserId, name, email, roles } of rootUsersConfig) {
    const user = new User({
      _id,
      oktaUserId,
      name,
      email,
      permissions: [
        {
          initiativeId: currentInitiativeId,
          permissions: roles
        }
      ]
    })
    await user.save()
  }
}

export const setupTree = async (
  root: InitiativeTreeFixture,
  utrvFixture: UtrvFixtureConfig[],
  surveyFixture?: SurveyFixtureConfig
) => {
  await setupInitiative(root.id, utrvFixture, surveyFixture ?? root.surveyFixture);
  if (!root.children) {
    return;
  }
  for (const c of root.children) {
    await setupTree(c, utrvFixture, surveyFixture);
  }
};

export const setupInitiative = async (
  currentInitiativeId: ObjectId,
  utrvFixture: UtrvFixtureConfig[],
  surveyFixture?: SurveyFixtureConfig
) => {
  const initiativeFixture = new Initiative({
    _id: currentInitiativeId,
    name: `initiative.name-${currentInitiativeId}`,
    code: `initiative.code-${currentInitiativeId}`,
  })

  const initiative = new Initiative(initiativeFixture)
  await initiative.save()

  const utrvWithInitiative = utrvFixture.map((utrv) => {
    utrv.utrv = {
      ...utrv.utrv,
      initiativeId: currentInitiativeId,
      period: surveyFixture?.period,
      effectiveDate: surveyFixture?.effectiveDate
    };
    return utrv;
  });
  const utrvs = await setupUtrvs(utrvWithInitiative);

  const surveyId = new ObjectId();
  await setupSurvey({
    _id: surveyId,
    code: `survey.code-${currentInitiativeId}`,
    effectiveDate: new Date(),
    initiativeId: initiative._id,
    visibleUtrvs: utrvs.map((u) => u._id),
    ...surveyFixture,
  });
}

export const setupSurvey = async (data: Partial<SurveyModelPlain>) => {
  const survey = new Survey({
    ...surveyOne,
    ...data,
    _id: data._id ?? new ObjectId(),
  })
  return survey.save()
}

export const setupUtr = async ({ code, valueType }: { code: string, valueType: UtrValueType }) => {
  const utrFixture = createUtrFromCode(code, { valueType, valueLabel: "label", type: 'gri', name: 'test utr' });
  const utrModel = new UniversalTracker(utrFixture);
  return utrModel.save()
}

export const setupInsightDashboard = async (props: Partial<InsightDashboardPlain> = {}) => {
  const dashboardFixture = createSimpleDashboard(props);
  const dashboardModel = new InsightDashboard(dashboardFixture);
  return dashboardModel.save();
};

export const setupDocument = async (props: Partial<DocumentPlain> = {}) => {
  const model = new Document(createDocument(props));
  return model.save();
};

export const setupUtrvs = async (utrvs: UtrvFixtureConfig[]) => {
  return UniversalTrackerValue.create(utrvs.map(({ utrv, utr }) => {
    const id = new ObjectId()
    const utrvFixture = createUtrv(
      id,
      utr._id,
      utrv.value,
      { universalTracker: utr, ...utrv },
      utrv.initiativeId
    );
    return new UniversalTrackerValue(utrvFixture)
  }))
}

export const setupBackgroundJob = async (data: Partial<BackgroundJobPlain>) => {
  const backgroundJob = new BackgroundJob({
    ...data,
  })
  return backgroundJob.save()
}

export const setupSurveyTemplate = async (data: Partial<SurveyTemplatePlain>) => {
  const surveyTemplate = new SurveyTemplate({
    ...data,
  });
  return surveyTemplate.save();
};

export const setupSurveyTemplateHistory = async (data: Partial<SurveyTemplateHistoryModelPlain>) => {
  const surveyTemplateHistory = new SurveyTemplateHistory({
    ...data,
  });
  return surveyTemplateHistory.save();
};

export const setupUser = async (data: Partial<UserPlain>) => {
  const user = new User({
    ...data,
  });
  return user.save();
};

export const setupOrganization = async (data: Partial<OrganizationPlain>) => {
  const organization = new Organization({
    ...data,
  });
  return organization.save();
};

export const setupMetricGroup = async (data: Partial<MetricGroupPlain>) => {
  const metricGroup = new MetricGroup({
    ...data,
  });
  return metricGroup.save();
};

export const clearFixtures = async (
  collections: Model<any>[] = [
    User,
    Organization,
    Survey,
    UniversalTrackerValue,
    UniversalTracker,
    Initiative,
    BackgroundJob,
    SurveyTemplate,
    SurveyTemplateHistory,
    MetricGroup,
  ]
) => {
  await Promise.all(collections.map((c) => c.deleteMany({})));
};
