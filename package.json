{"name": "api.g17.eco", "version": "1.0.0", "description": "G17Eco Api", "scripts": {"artifactregistry-login": "npx google-artifactregistry-auth --repo-config=$HOME/.npmrc", "start": "TZ=UTC npm run watch", "ts": "NODE_OPTIONS='--no-experimental-strip-types' npx ts-node-dev --no-deps --respawn --transpile-only server/app.ts", "ts-debug": "NODE_OPTIONS='--no-experimental-strip-types' npx ts-node-dev --inspect --no-deps --respawn --transpile-only server/app.ts", "serve": "nodemon dist/app.js", "build-ts": "tsc -p tsconfig-build.json ; cp -R server/files dist", "watch-ts": "tsc -w -p tsconfig-build.json", "watch-tsgo": "npx tsgo -w -p tsconfig-build.json", "watch": "concurrently -k -p \"[{name}]\" \"npm run watch-ts\" \"npm run serve\"", "test": "NODE_OPTIONS='--no-experimental-strip-types' NODE_ENV=test TS_NODE_TRANSPILE_ONLY=true TZ=UTC mocha -r ts-node/register 'tests/**/*.spec.ts' --timeout=5000", "test:file": "NODE_OPTIONS='--no-experimental-strip-types' NODE_ENV=test TS_NODE_TRANSPILE_ONLY=true TZ=UTC mocha -r ts-node/register --timeout=5000", "test-coverage": "nyc --reporter=html --reporter=text npm run test", "test-codecov": "nyc --reporter=lcov npm run test", "debug": "npm run build && npm run watch-debug", "serve-debug": "nodemon --inspect dist/app.js", "watch-debug": "concurrently -k -p \"[{name}]\" \"npm run watch-ts\" \"npm run serve-debug\"", "test-integration": "./deploy/integration.sh http://localhost:4003", "materiality-assessment": "NODE_OPTIONS='--no-experimental-strip-types' npx ts-node server/scripts/materiality-assessment/generate.ts", "background-jobs": "NODE_OPTIONS='--no-experimental-strip-types' npx ts-node server/scripts/background.ts", "lint": "oxlint --import-plugin --tsconfig=./tsconfig.json server && eslint server"}, "repository": {"type": "git", "url": "git+ssh://*****************/worldwidegenerationteam/api.g17.eco.git"}, "engines": {"npm": ">=10.9.2", "node": ">=22.14.0"}, "author": "<PERSON>", "license": "ISC", "homepage": "https://bitbucket.org/worldwidegenerationteam/api.g17.eco#readme", "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@aws-sdk/client-qldb": "^3.529.0", "@aws-sdk/client-ses": "^3.529.0", "@aws-sdk/client-sqs": "^3.529.0", "@azure/storage-blob": "^12.24.0", "@cacheable/node-cache": "^1.5.8", "@g17eco/convert-units": "^2.14.0", "@g17eco/core": "^3.9.0", "@google-cloud/logging-winston": "^6.0.0", "@google-cloud/run": "^1.3.0", "@google-cloud/storage": "^7.12.1", "@kubernetes/client-node": "^0.21.0", "@lexical/headless": "^0.33.0", "@lexical/html": "^0.33.0", "@lexical/list": "^0.33.0", "@lexical/rich-text": "^0.33.0", "@magicbell/core": "^5.1.0", "@mailchimp/mailchimp_marketing": "^3.0.80", "@okta/jwt-verifier": "^3.2.2", "@okta/okta-sdk-nodejs": "^6.6.0", "@sentry/cli": "^2.32.2", "@sentry/node": "^8.18.0", "@sheet/core": "^1.20220921.1", "@slack/web-api": "^7.9.3", "@xmldom/xmldom": "^0.8.10", "amazon-qldb-driver-nodejs": "^3.1.0", "analytics-node": "^6.2.0", "archiver": "^7.0.0", "axios": "^1.7.4", "bcrypt": "^5.1.0", "compression": "^1.7.4", "concurrently": "^7.6.0", "cors": "^2.8.5", "cron-parser": "^4.7.1", "csv-parse": "^5.5.2", "csv-writer": "^1.6.0", "dayjs": "^1.11.13", "dotenv": "^16.0.3", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-redis-cache": "^1.1.3", "express-xml-bodyparser": "^0.3.0", "gics": "^2.1.1", "ion-js": "^5.2.1", "isomorphic-dompurify": "^2.14.0", "jsbi": "^3.2.5", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.0", "lexical": "^0.33.0", "lodash.isequal": "^4.5.0", "logform": "^2.6.0", "mathjs": "^13.0.3", "mime-types": "^2.1.35", "moment": "^2.29.4", "mongodb-memory-server": "^10.1.2", "mongoose": "^8.12.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nanoid": "^3.3.4", "nocache": "^3.0.4", "node-cron": "^3.0.2", "openai": "^4.103.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pg": "^8.8.0", "pptx-automizer": "^0.6.7", "prefixed-api-key": "^1.1.1", "rate-limit-redis": "^4.2.0", "redis": "^4.7.0", "rimraf": "^4.0.4", "sanitize-filename": "^1.6.3", "sequelize": "^6.29.0", "simple-git": "^3.21.0", "socks": "^2.7.4", "ssh2-sftp-client": "^9.0.4", "stripe": "^16.12.0", "ts-exif-parser": "^0.2.2", "ua-parser-js": "^1.0.33", "uuid": "^9.0.0", "winston": "^3.9.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.31.0", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/analytics-node": "^3.1.9", "@types/archiver": "^5.3.1", "@types/bcrypt": "^5.0.0", "@types/body-parser": "^1.19.2", "@types/chai": "^4.3.4", "@types/chai-as-promised": "^7.1.5", "@types/compression": "^1.7.2", "@types/convert-units": "^2.3.5", "@types/cors": "^2.8.13", "@types/express": "^5.0.1", "@types/express-xml-bodyparser": "^0.3.2", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.1", "@types/lodash.isequal": "^4.5.8", "@types/mailchimp__mailchimp_marketing": "^3.0.20", "@types/mime-types": "^2.1.1", "@types/mocha": "^10.0.1", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^22.14.1", "@types/node-cron": "^3.0.7", "@types/passport-jwt": "^3.0.8", "@types/sinon": "^10.0.13", "@types/ssh2-sftp-client": "^9.0.0", "@types/ua-parser-js": "^0.7.36", "@types/uuid": "^9.0.0", "@types/yargs": "^17.0.33", "@typescript/native-preview": "^7.0.0-dev.20250622.1", "chai": "^4.3.7", "chai-as-promised": "^7.1.1", "eslint": "^9.31.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-oxlint": "^1.8.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.3.0", "mocha": "^10.2.0", "nodemon": "^2.0.20", "nyc": "^15.1.0", "oxlint": "^1.8.0", "sinon": "^15.0.1", "source-map-support": "^0.5.21", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.5.4", "typescript-eslint": "^8.38.0", "yargs": "^18.0.0"}}